# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api

# Database Configuration (for backend)
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=vendorms
DATABASE_USER=postgres
DATABASE_PASSWORD=password
DATABASE_SSL=false

# Authentication
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=pdf,doc,docx,jpg,jpeg,png

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Redis Configuration (for caching)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Development
NODE_ENV=development
PORT=3001