# VendorMS Project Structure

## Root Directory Organization

```
├── .git/                    # Git version control
├── .kiro/                   # Kiro IDE configuration and steering
├── Plan/                    # Project planning documents
│   ├── Architecture & Tech Decisions/
│   └── Features/           # Feature specifications
├── public/                 # Static assets (favicon, manifest, etc.)
├── src/                    # Main source code
└── Configuration files     # Build and tooling configs
```

## Source Code Structure (`src/`)

### Core Application Files
- `main.tsx` - Application entry point with React root
- `App.tsx` - Main app component with routing and providers
- `index.css` - Global styles and CSS variables
- `vite-env.d.ts` - Vite type definitions

### Component Organization
```
src/components/
├── Layout/                 # Layout components (AppLayout, Sidebar, Header)
├── ui/                     # Shadcn/ui component library
│   ├── button.tsx         # Reusable UI primitives
│   ├── card.tsx
│   ├── form.tsx
│   └── ...                # 40+ UI components
└── ProtectedRoute.tsx     # Route protection HOC
```

### Page Components (`src/pages/`)
- **Authentication**: `Login.tsx`
- **Core Pages**: `Dashboard.tsx`, `Index.tsx`, `NotFound.tsx`
- **Vendor Management**: `VendorsList.tsx`, `VendorOnboard.tsx`, `VendorProfile.tsx`, `VendorEdit.tsx`
- **Contract Management**: `ContractsList.tsx`, `ContractCreate.tsx`, `ContractView.tsx`

### State Management (`src/store/`)
```
src/store/
├── index.ts               # Store configuration
└── slices/                # Feature-based Redux slices
    ├── authSlice.ts       # Authentication state
    ├── vendorsSlice.ts    # Vendor management state
    ├── contractsSlice.ts  # Contract management state
    ├── invoicesSlice.ts   # Invoice processing state
    ├── notificationsSlice.ts # Notification system
    └── uiSlice.ts         # UI state (theme, sidebar, etc.)
```

### Custom Hooks (`src/hooks/`)
- `useAuth.ts` - Authentication logic and permissions
- `use-toast.ts` - Toast notification management
- `use-mobile.tsx` - Mobile device detection

### Utilities (`src/lib/`)
- `utils.ts` - Common utility functions (cn, clsx helpers)

## Routing Structure

The application uses nested routing with role-based protection:

```
/ (root)
├── /login                 # Public authentication
├── /register             # Public registration
├── /forgot-password      # Public password reset
└── / (protected)         # Main app layout
    ├── /dashboard        # Main dashboard
    ├── /vendors/         # Vendor management
    │   ├── /list
    │   ├── /onboard      # Manager+ only
    │   ├── /:id/profile
    │   └── /:id/edit     # Manager+ only
    ├── /contracts/       # Contract management
    │   ├── /list
    │   └── /create       # Manager+ only
    ├── /invoices/        # Invoice processing
    │   ├── /list
    │   └── /generate     # Manager+ only
    ├── /performance/     # Performance tracking
    │   ├── /scorecards
    │   └── /risks
    ├── /analytics/       # Analytics and reporting
    │   ├── /dashboard
    │   ├── /reports
    │   └── /ai-insights
    ├── /workflows/       # Workflow management
    │   ├── /list         # Manager+ only
    │   └── /create       # Admin only
    └── /admin/           # Administration
        ├── /users        # Admin only
        ├── /settings     # Admin only
        └── /backup       # Admin only
```

## File Naming Conventions

- **Components**: PascalCase (e.g., `VendorsList.tsx`, `ProtectedRoute.tsx`)
- **Hooks**: camelCase with `use` prefix (e.g., `useAuth.ts`)
- **Utilities**: camelCase (e.g., `utils.ts`)
- **Slices**: camelCase with `Slice` suffix (e.g., `authSlice.ts`)
- **Types**: PascalCase interfaces/types in component files or separate `.types.ts` files

## Import Patterns

- **Path Alias**: Use `@/` for all internal imports from `src/`
- **Component Imports**: Import from `@/components/ui/` for UI components
- **Hook Imports**: Import from `@/hooks/`
- **Store Imports**: Import from `@/store/` and `@/store/slices/`
- **Utility Imports**: Import from `@/lib/utils`

## Role-Based Access Control

Components and routes implement hierarchical permissions:
- **Admin** (level 3): Full system access
- **Manager** (level 2): Can edit, create, approve
- **Viewer** (level 1): Read-only access

Protection is implemented through:
- `ProtectedRoute` component with `requiredRole` prop
- `useAuth` hook with permission checking methods
- Role hierarchy validation in authentication slice

## Development Patterns

- **Component Structure**: Functional components with TypeScript
- **State Management**: Redux Toolkit slices for global state, local state for component-specific data
- **Form Handling**: React Hook Form with Yup validation
- **Error Handling**: Try-catch blocks with toast notifications
- **Loading States**: Consistent loading UI patterns
- **Responsive Design**: Mobile-first approach with Tailwind breakpoints