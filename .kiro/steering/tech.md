# VendorMS Technical Stack

## Build System & Development

- **Build Tool**: Vite with React SWC plugin for fast development and builds
- **Package Manager**: npm (with bun.lockb present, indicating Bun compatibility)
- **TypeScript**: Full TypeScript implementation across the codebase
- **Development Server**: Vite dev server on port 8080

## Frontend Stack

- **Framework**: React 18 with modern hooks and concurrent features
- **Routing**: React Router DOM v6 with nested routes and protected routes
- **State Management**: Redux Toolkit with feature-based slices
- **Query Management**: TanStack Query (React Query) for server state
- **Styling**: Tailwind CSS with custom design system and neumorphism theme
- **UI Components**: Shadcn/ui built on Radix UI primitives
- **Icons**: Lucide React icon library
- **Animations**: Framer Motion for micro-interactions
- **Charts**: Recharts for data visualization

## Key Libraries

- **Forms**: React Hook Form with Yup validation schemas
- **HTTP Client**: Axios for API communication
- **Real-time**: Socket.io client for live updates
- **Internationalization**: i18next with react-i18next
- **Notifications**: Sonner for toast notifications
- **Date Handling**: date-fns for date utilities
- **Currency**: currency.js for financial calculations

## Development Tools

- **Linting**: ESLint with TypeScript and React plugins
- **Code Quality**: TypeScript strict mode enabled
- **Path Aliases**: `@/` alias pointing to `src/` directory
- **Component Tagging**: Lovable-tagger for development mode

## Common Commands

```bash
# Development
npm run dev          # Start development server on port 8080
npm run build        # Production build
npm run build:dev    # Development build
npm run preview      # Preview production build
npm run lint         # Run ESLint

# Alternative with Bun
bun dev             # Start development server
bun run build       # Production build
```

## Architecture Patterns

- **Component Structure**: Feature-based organization with shared UI components
- **State Management**: Redux slices for global state, local state for component-specific data
- **Route Protection**: HOC pattern with role-based access control
- **API Layer**: Service layer pattern with centralized HTTP client
- **Type Safety**: Strict TypeScript with proper type definitions
- **Error Handling**: Global error boundaries and toast notifications

## Design System

- **Theme**: Custom Tailwind configuration with CSS variables
- **Colors**: HSL-based color system with semantic tokens
- **Typography**: Inter font family with consistent hierarchy
- **Spacing**: 8px grid system for layouts
- **Animations**: Custom keyframes with consistent timing (300ms cubic-bezier)
- **Components**: Neumorphism design with soft shadows and rounded corners

## Configuration Files

- `vite.config.ts`: Vite configuration with React SWC and path aliases
- `tailwind.config.ts`: Custom Tailwind theme with extended colors and animations
- `tsconfig.json`: TypeScript configuration with strict mode
- `eslint.config.js`: ESLint rules for code quality
- `components.json`: Shadcn/ui component configuration