# VendorMS - Modern Vendor Management System

A comprehensive, modern Vendor Management System built with React, TypeScript, and cutting-edge technologies. Features beautiful neumorphism design, role-based access control, real-time collaboration, and multi-language support.

## 🚀 Features

### Core Functionality
- **Vendor Management**: Complete vendor lifecycle from onboarding to performance tracking
- **Contract Management**: Create, track, and manage vendor contracts with milestones
- **Invoice Processing**: Generate, approve, and process vendor invoices
- **Performance Analytics**: Real-time dashboards and AI-powered insights
- **Risk Assessment**: Automated compliance monitoring and risk scoring
- **Workflow Automation**: Custom workflow builder and execution

### Design & UX
- **Neumorphism UI**: Modern soft UI design with subtle shadows and gradients
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Dark/Light Mode**: Seamless theme switching with system preference detection
- **Micro-animations**: Delightful interactions with Framer Motion
- **Accessibility**: WCAG 2.2 compliant with full keyboard navigation

### Technical Features
- **Role-Based Access Control (RBAC)**: Admin, Manager, and Viewer roles
- **Real-time Updates**: Socket.io integration for live collaboration
- **Multi-language Support**: i18next with EN/ES/FR translations
- **Multi-currency**: Support for USD/EUR/GBP with automatic conversion
- **PWA Ready**: Offline support and installable experience
- **State Management**: Redux Toolkit for global state
- **Form Validation**: React Hook Form with Yup schemas

## 🛠 Tech Stack

### Frontend
- **React 18** - Modern UI library with concurrent features
- **TypeScript** - Type-safe development
- **Vite** - Lightning-fast build tool
- **Tailwind CSS** - Utility-first styling with custom design system
- **Framer Motion** - Smooth animations and transitions

### State Management
- **Redux Toolkit** - Predictable state management
- **React Redux** - React bindings for Redux
- **Context API** - Local component state

### UI Components
- **Shadcn/ui** - Customizable component library
- **Radix UI** - Accessible primitive components
- **Lucide React** - Beautiful icon library
- **Recharts** - Responsive chart library

### Forms & Validation
- **React Hook Form** - Performant forms with minimal re-renders
- **Yup** - Schema-based validation
- **@hookform/resolvers** - Integration layer

### Networking & Data
- **Axios** - HTTP client for API calls
- **TanStack Query** - Server state management
- **Socket.io Client** - Real-time communication

### Internationalization
- **i18next** - Internationalization framework
- **react-i18next** - React integration for i18n

### Development
- **ESLint** - Code linting and quality
- **TypeScript ESLint** - TypeScript-specific rules
- **Vite PWA** - Progressive Web App features

## 🏗 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── Layout/         # Layout components (Sidebar, Header)
│   ├── ui/             # Shadcn UI components
│   └── ProtectedRoute.tsx
├── pages/              # Page components
│   ├── Login.tsx       # Authentication page
│   ├── Dashboard.tsx   # Main dashboard
│   ├── VendorsList.tsx # Vendor management
│   └── ...
├── store/              # Redux store and slices
│   ├── slices/         # Feature-specific slices
│   └── index.ts        # Store configuration
├── hooks/              # Custom React hooks
│   ├── useAuth.ts      # Authentication logic
│   └── use-toast.ts    # Toast notifications
├── utils/              # Utility functions
├── services/           # API service layer
├── assets/             # Static assets
├── App.tsx             # Main app component
└── main.tsx            # App entry point
```

## 🚦 Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- Modern browser with ES2020+ support

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd vendorms
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Start development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:8080`

### Demo Credentials

The application includes mock authentication with the following demo accounts:

- **Admin**: `<EMAIL>` / `password`
  - Full system access including user management and system settings
- **Manager**: `<EMAIL>` / `password`
  - Can edit vendors, create contracts, approve invoices
- **Viewer**: `<EMAIL>` / `password`
  - Read-only access to view data and reports

## 📱 Features Overview

### Dashboard
- Real-time metrics and KPIs
- Recent activity feed
- Quick action buttons
- Alert notifications
- Performance indicators

### Vendor Management
- Vendor onboarding workflow
- Profile management with custom fields
- Performance scoring and tracking
- Document management
- Certification tracking

### Contract Management
- Contract lifecycle management
- Milestone tracking
- DocuSign integration (mock)
- Amendment history
- Renewal alerts

### Invoice Processing
- Invoice generation from contracts
- Multi-step approval workflow
- Payment processing (Stripe/PayPal mock)
- Tax and penalty calculations
- Dispute management

### Analytics & Reporting
- Interactive dashboards
- Custom report builder
- Export to PDF/Excel
- AI-powered insights
- Performance trends

### Administration
- User management with role assignment
- System configuration
- Backup and restore
- Audit logs
- Custom field definitions

## 🎨 Design System

The application uses a modern neumorphism design system with:

- **Soft Shadows**: Subtle elevation with inner/outer shadows
- **Rounded Corners**: Consistent border radius throughout
- **Smooth Transitions**: 300ms cubic-bezier animations
- **Color Harmony**: HSL-based color palette with semantic tokens
- **Typography**: Inter font family with proper hierarchy
- **Spacing**: 8px grid system for consistent layouts

### Color Palette
- **Primary**: Blue tones for actions and emphasis
- **Success**: Green for positive states
- **Warning**: Amber for caution states
- **Destructive**: Red for dangerous actions
- **Muted**: Gray tones for secondary content

## 🔒 Security Features

- **JWT Authentication**: Secure token-based auth
- **Role-Based Permissions**: Granular access control
- **Input Validation**: Client and server-side validation
- **XSS Protection**: Sanitized user inputs
- **CSRF Protection**: Cross-site request forgery prevention

## 🌐 Internationalization

Supports multiple languages with:
- English (en)
- Spanish (es)
- French (fr)

Translation keys are organized by feature modules for easy maintenance.

## 📱 PWA Features

- **Offline Support**: Cache critical pages and assets
- **Install Prompt**: Native app-like installation
- **Push Notifications**: Real-time alerts (when backend available)
- **Background Sync**: Queue actions when offline

## 🧪 Testing Strategy

- **Unit Tests**: Jest with React Testing Library
- **Integration Tests**: API and component integration
- **E2E Tests**: Playwright for user journey testing
- **Accessibility Tests**: axe-core integration

## 📈 Performance Optimizations

- **Code Splitting**: Route-based chunks
- **Lazy Loading**: Dynamic imports for heavy components
- **Image Optimization**: WebP format with fallbacks
- **Bundle Analysis**: Webpack Bundle Analyzer integration
- **Caching Strategy**: Service worker with cache-first approach

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm run build
npx vercel --prod
```

### Netlify
```bash
npm run build
# Deploy dist/ folder to Netlify
```

### Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 8080
CMD ["npm", "run", "preview"]
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For questions and support:
- 📧 Email: <EMAIL>
- 💬 Discord: [VendorMS Community](https://discord.gg/vendorms)
- 📖 Documentation: [docs.vendorms.com](https://docs.vendorms.com)

## 🗺 Roadmap

### Phase 1 (Current)
- ✅ Core vendor management
- ✅ Authentication and RBAC
- ✅ Dashboard and analytics
- ✅ Modern UI with neumorphism

### Phase 2 (Q2 2025)
- 🔄 Real-time collaboration
- 🔄 Advanced reporting
- 🔄 Mobile app (React Native)
- 🔄 API integrations

### Phase 3 (Q3 2025)
- 📅 AI-powered recommendations
- 📅 Blockchain verification
- 📅 Advanced workflow automation
- 📅 Multi-tenant architecture

---

Built with ❤️ using modern web technologies and best practices.