const Vendor = require('../models/Vendor');
const Joi = require('joi');

// Validation schemas
const vendorCreateSchema = Joi.object({
  name: Joi.string().min(2).max(255).required(),
  contact_email: Joi.string().email().max(255).required(),
  contact_phone: Joi.string().pattern(/^[\+]?[1-9][\d]{0,15}$/).max(50).required(),
  address: Joi.object({
    street: Joi.string().max(255).required(),
    city: Joi.string().max(100).required(),
    state: Joi.string().max(100).required(),
    zip: Joi.string().max(20).required(),
    country: Joi.string().max(100).required(),
  }).required(),
  category: Joi.string().max(100).required(),
  certifications: Joi.array().items(Joi.string().max(100)).default([]),
  custom_fields: Joi.object().default({}),
});

const vendorUpdateSchema = Joi.object({
  name: Joi.string().min(2).max(255),
  contact_email: Joi.string().email().max(255),
  contact_phone: Joi.string().pattern(/^[\+]?[1-9][\d]{0,15}$/).max(50),
  address: Joi.object({
    street: Joi.string().max(255),
    city: Joi.string().max(100),
    state: Joi.string().max(100),
    zip: Joi.string().max(20),
    country: Joi.string().max(100),
  }),
  category: Joi.string().max(100),
  certifications: Joi.array().items(Joi.string().max(100)),
  custom_fields: Joi.object(),
});

// Get all vendors
const getVendors = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const filters = {
      search: req.query.search,
      category: req.query.category,
      status: req.query.status,
      performance_min: req.query.performance_min ? parseFloat(req.query.performance_min) : undefined,
      performance_max: req.query.performance_max ? parseFloat(req.query.performance_max) : undefined,
    };

    const result = await Vendor.findAll(filters, page, limit);
    
    res.json({
      success: true,
      data: result.vendors,
      pagination: result.pagination,
    });
  } catch (error) {
    console.error('Error fetching vendors:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch vendors',
      error: error.message,
    });
  }
};

// Get vendor by ID
const getVendorById = async (req, res) => {
  try {
    const { id } = req.params;
    const vendor = await Vendor.findById(id);
    
    if (!vendor) {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found',
      });
    }

    res.json({
      success: true,
      data: vendor,
    });
  } catch (error) {
    console.error('Error fetching vendor:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch vendor',
      error: error.message,
    });
  }
};

// Create new vendor
const createVendor = async (req, res) => {
  try {
    // Validate request body
    const { error, value } = vendorCreateSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details.reduce((acc, detail) => {
          acc[detail.path.join('.')] = detail.message;
          return acc;
        }, {}),
      });
    }

    // TODO: Extract user ID from JWT token
    const userId = 1; // Hardcoded for now
    const vendor = await Vendor.create(value, userId);
    
    res.status(201).json({
      success: true,
      data: vendor,
      message: 'Vendor created successfully',
    });
  } catch (error) {
    console.error('Error creating vendor:', error);
    
    // Handle unique constraint violations
    if (error.code === '23505') {
      return res.status(409).json({
        success: false,
        message: 'A vendor with this email already exists',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to create vendor',
      error: error.message,
    });
  }
};

// Update vendor
const updateVendor = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Validate request body
    const { error, value } = vendorUpdateSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        success: false,
        message: 'Validation error',
        details: error.details.reduce((acc, detail) => {
          acc[detail.path.join('.')] = detail.message;
          return acc;
        }, {}),
      });
    }

    // TODO: Extract user ID from JWT token
    const userId = 1; // Hardcoded for now
    const vendor = await Vendor.update(id, value, userId);
    
    res.json({
      success: true,
      data: vendor,
      message: 'Vendor updated successfully',
    });
  } catch (error) {
    console.error('Error updating vendor:', error);
    
    if (error.message === 'Vendor not found') {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to update vendor',
      error: error.message,
    });
  }
};

// Delete vendor (soft delete)
const deleteVendor = async (req, res) => {
  try {
    const { id } = req.params;
    // TODO: Extract user ID from JWT token
    const userId = 1; // Hardcoded for now
    const result = await Vendor.delete(id, userId);
    
    res.json({
      success: true,
      data: result,
      message: 'Vendor deactivated successfully',
    });
  } catch (error) {
    console.error('Error deleting vendor:', error);
    
    if (error.message === 'Vendor not found') {
      return res.status(404).json({
        success: false,
        message: 'Vendor not found',
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to deactivate vendor',
      error: error.message,
    });
  }
};

// Get vendor categories
const getVendorCategories = async (req, res) => {
  try {
    const categories = await Vendor.getCategories();
    
    res.json({
      success: true,
      data: categories,
    });
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch categories',
      error: error.message,
    });
  }
};

// Get vendor audit history
const getVendorAudit = async (req, res) => {
  try {
    const { id } = req.params;
    const auditHistory = await Vendor.getAuditHistory(id);
    
    res.json({
      success: true,
      data: auditHistory,
    });
  } catch (error) {
    console.error('Error fetching audit history:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch audit history',
      error: error.message,
    });
  }
};

module.exports = {
  getVendors,
  getVendorById,
  createVendor,
  updateVendor,
  deleteVendor,
  getVendorCategories,
  getVendorAudit,
};