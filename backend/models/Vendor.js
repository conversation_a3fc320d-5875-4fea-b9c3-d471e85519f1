const { query, queryWithUser, beginTransaction, commitTransaction, rollbackTransaction } = require('../config/database');

class Vendor {
  // Get all vendors with filtering and pagination
  static async findAll(filters = {}, page = 1, limit = 10) {
    let whereClause = 'WHERE 1=1';
    const params = [];
    let paramCount = 0;

    // Apply filters
    if (filters.search) {
      paramCount++;
      whereClause += ` AND (name ILIKE $${paramCount} OR contact_email ILIKE $${paramCount} OR category ILIKE $${paramCount})`;
      params.push(`%${filters.search}%`);
    }

    if (filters.category) {
      paramCount++;
      whereClause += ` AND category = $${paramCount}`;
      params.push(filters.category);
    }

    if (filters.status) {
      paramCount++;
      whereClause += ` AND status = $${paramCount}`;
      params.push(filters.status);
    }

    if (filters.performance_min !== undefined) {
      paramCount++;
      whereClause += ` AND performance_score >= $${paramCount}`;
      params.push(filters.performance_min);
    }

    if (filters.performance_max !== undefined) {
      paramCount++;
      whereClause += ` AND performance_score <= $${paramCount}`;
      params.push(filters.performance_max);
    }

    // Count total records
    const countQuery = `SELECT COUNT(*) FROM vendors ${whereClause}`;
    const countResult = await query(countQuery, params);
    const total = parseInt(countResult.rows[0].count);

    // Get paginated results
    const offset = (page - 1) * limit;
    paramCount++;
    const limitParam = paramCount;
    paramCount++;
    const offsetParam = paramCount;
    
    const selectQuery = `
      SELECT id, name, contact_email, contact_phone, address, category, 
             certifications, performance_score, status, custom_fields,
             created_at, updated_at, deactivated_at, blacklisted_reason
      FROM vendors 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT $${limitParam} OFFSET $${offsetParam}
    `;
    
    params.push(limit, offset);
    const result = await query(selectQuery, params);

    return {
      vendors: result.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  // Find vendor by ID
  static async findById(id) {
    const result = await query(
      `SELECT id, name, contact_email, contact_phone, address, category, 
              certifications, performance_score, status, custom_fields,
              created_at, updated_at, deactivated_at, blacklisted_reason
       FROM vendors WHERE id = $1`,
      [id]
    );
    return result.rows[0];
  }

  // Create new vendor
  static async create(vendorData, userId = 1) {
    const {
      name,
      contact_email,
      contact_phone,
      address,
      category,
      certifications = [],
      custom_fields = {}
    } = vendorData;

    const result = await queryWithUser(
      `INSERT INTO vendors (name, contact_email, contact_phone, address, category, certifications, custom_fields)
       VALUES ($1, $2, $3, $4, $5, $6, $7)
       RETURNING id, name, contact_email, contact_phone, address, category, 
                 certifications, performance_score, status, custom_fields,
                 created_at, updated_at, deactivated_at, blacklisted_reason`,
      [name, contact_email, contact_phone, JSON.stringify(address), category, JSON.stringify(certifications), JSON.stringify(custom_fields)],
      userId
    );

    return result.rows[0];
  }

  // Update vendor
  static async update(id, vendorData, userId = 1) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);
      
      // Get current vendor data for audit
      const currentResult = await client.query('SELECT * FROM vendors WHERE id = $1', [id]);
      if (currentResult.rows.length === 0) {
        throw new Error('Vendor not found');
      }
      const currentVendor = currentResult.rows[0];

      // Build update query dynamically
      const updateFields = [];
      const params = [id];
      let paramCount = 1;

      Object.keys(vendorData).forEach(key => {
        if (vendorData[key] !== undefined && key !== 'id') {
          paramCount++;
          if (key === 'address' || key === 'certifications' || key === 'custom_fields') {
            updateFields.push(`${key} = $${paramCount}`);
            params.push(JSON.stringify(vendorData[key]));
          } else {
            updateFields.push(`${key} = $${paramCount}`);
            params.push(vendorData[key]);
          }
        }
      });

      if (updateFields.length === 0) {
        await rollbackTransaction(client);
        return currentVendor;
      }

      // Add updated_at
      paramCount++;
      updateFields.push(`updated_at = $${paramCount}`);
      params.push(new Date());

      const updateQuery = `
        UPDATE vendors 
        SET ${updateFields.join(', ')}
        WHERE id = $1
        RETURNING id, name, contact_email, contact_phone, address, category, 
                  certifications, performance_score, status, custom_fields,
                  created_at, updated_at, deactivated_at, blacklisted_reason
      `;

      const result = await client.query(updateQuery, params);
      const updatedVendor = result.rows[0];

      // Audit logging is handled by database trigger

      await commitTransaction(client);
      return updatedVendor;
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Soft delete vendor
  static async delete(id, userId = 1) {
    const client = await beginTransaction();
    
    try {
      // Set user context for audit logging
      await client.query('SELECT set_config($1, $2, true)', ['app.current_user_id', userId.toString()]);
      
      // Get current vendor data
      const currentResult = await client.query('SELECT * FROM vendors WHERE id = $1', [id]);
      if (currentResult.rows.length === 0) {
        throw new Error('Vendor not found');
      }

      // Soft delete
      const result = await client.query(
        `UPDATE vendors 
         SET status = 'inactive', deactivated_at = $1, updated_at = $1
         WHERE id = $2
         RETURNING id`,
        [new Date(), id]
      );

      // Audit logging is handled by database trigger

      await commitTransaction(client);
      return result.rows[0];
    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Get vendor categories
  static async getCategories() {
    const result = await query(
      'SELECT DISTINCT category FROM vendors WHERE category IS NOT NULL ORDER BY category'
    );
    return result.rows.map(row => row.category);
  }

  // Get vendor audit history
  static async getAuditHistory(id) {
    const result = await query(
      `SELECT a.id, a.entity_type, a.entity_id, a.action, a.user_id, 
              a.old_value, a.new_value, a.details, a.timestamp,
              u.email as user_name
       FROM audits a
       LEFT JOIN users u ON a.user_id = u.id
       WHERE a.entity_type = 'vendors' AND a.entity_id = $1
       ORDER BY a.timestamp DESC`,
      [id]
    );
    return result.rows;
  }
}

module.exports = Vendor;