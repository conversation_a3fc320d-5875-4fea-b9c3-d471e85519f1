{"name": "vendorms-backend", "version": "1.0.0", "description": "VendorMS Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "pg": "^8.11.3", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "morgan": "^1.10.0", "compression": "^1.7.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}, "keywords": ["vendor", "management", "api", "postgresql"], "author": "VendorMS Team", "license": "MIT"}