const express = require('express');
const router = express.Router();
const {
  getVendors,
  getVendorById,
  createVendor,
  updateVendor,
  deleteVendor,
  getVendorCategories,
  getVendorAudit,
} = require('../controllers/vendorController');

// Routes
router.get('/', getVendors);
router.get('/categories', getVendorCategories);
router.get('/:id', getVendorById);
router.get('/:id/audit', getVendorAudit);
router.post('/', createVendor);
router.put('/:id', updateVendor);
router.delete('/:id', deleteVendor);

module.exports = router;