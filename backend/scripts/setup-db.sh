#!/bin/bash

echo "🚀 Setting up VendorMS Database..."

# Get current user
CURRENT_USER=$(whoami)
echo "👤 Current user: $CURRENT_USER"

# Create database user if it doesn't exist
echo "📝 Creating database user..."
createuser -s $CURRENT_USER 2>/dev/null || echo "User $CURRENT_USER already exists"

# Create database
echo "🗄️ Creating vendorms database..."
createdb vendorms 2>/dev/null || echo "Database vendorms already exists"

# Run schema
echo "📋 Running database schema..."
psql -d vendorms -f ../database/schema.sql

echo "✅ Database setup complete!"
echo ""
echo "Update your backend/.env file with:"
echo "DATABASE_USER=$CURRENT_USER"
echo "DATABASE_PASSWORD="
echo "DATABASE_NAME=vendorms"