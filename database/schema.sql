-- VendorMS Database Schema
-- PostgreSQL 17.x compatible schema for comprehensive Vendor Management System
-- Based on detailed database plan and ER diagram

-- ============================================================================
-- 1. EXTENSIONS AND CONFIGURATION
-- ============================================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS pgcrypto;  -- For encryption (passwords, 2FA secrets)
CREATE EXTENSION IF NOT EXISTS pg_trgm;   -- For trigram fuzzy searches
CREATE EXTENSION IF NOT EXISTS pgaudit;   -- For database-level auditing

-- Configure auditing (adjust per environment)
-- SET pgaudit.log = 'all';

-- ============================================================================
-- 2. CUSTOM TYPES AND ENUMS
-- ============================================================================

-- User roles with hierarchical permissions
CREATE TYPE role_enum AS ENUM ('admin', 'manager', 'viewer');

-- Vendor lifecycle statuses
CREATE TYPE vendor_status_enum AS ENUM ('active', 'inactive', 'blacklisted');

-- Contract lifecycle statuses
CREATE TYPE contract_status_enum AS ENUM ('draft', 'ready_for_signature', 'signed', 'active', 'terminated', 'disputed');

-- Invoice processing statuses
CREATE TYPE invoice_status_enum AS ENUM ('draft', 'approved', 'paid', 'partially_paid', 'disputed');

-- Payment processing statuses
CREATE TYPE payment_status_enum AS ENUM ('pending', 'completed', 'failed', 'refunded');

-- Compliance and risk assessment types
CREATE TYPE compliance_type_enum AS ENUM ('GDPR', 'ISO_27001', 'SOX', 'HIPAA');

-- Workflow execution statuses
CREATE TYPE workflow_status_enum AS ENUM ('pending', 'running', 'completed', 'failed', 'escalated');

-- Notification delivery methods
CREATE TYPE notification_type_enum AS ENUM ('email', 'sms');

-- AI insight categories
CREATE TYPE ai_insight_type_enum AS ENUM ('risk_prediction', 'vendor_recommendation');

-- Supported languages for internationalization
CREATE TYPE language_enum AS ENUM ('en', 'es', 'fr', 'de', 'zh');

-- Supported currencies (ISO 4217 codes)
CREATE TYPE currency_enum AS ENUM ('USD', 'EUR', 'GBP', 'JPY', 'CNY');

-- KPI measurement types
CREATE TYPE kpi_type_enum AS ENUM ('delivery_time', 'quality', 'cost');

-- Payment gateway providers
CREATE TYPE gateway_enum AS ENUM ('stripe', 'paypal');

-- Alert types for system notifications
CREATE TYPE alert_type_enum AS ENUM ('risk', 'performance', 'workflow_escalation');

-- General status for various entities
CREATE TYPE general_status_enum AS ENUM ('open', 'resolved', 'dismissed', 'escalated');

-- Audit action types
CREATE TYPE audit_action_enum AS ENUM ('create', 'update', 'delete', 'view', 'approve');

-- Integration types
CREATE TYPE integration_type_enum AS ENUM ('quickbooks', 'salesforce', 'docusign', 'stripe', 'paypal', 'twilio');

-- ============================================================================
-- 3. CORE TABLES
-- ============================================================================

-- Users Table (Authentication & Authorization)
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password BYTEA NOT NULL,  -- Encrypted with pgp_sym_encrypt
    role role_enum NOT NULL DEFAULT 'viewer',
    is_verified BOOLEAN DEFAULT FALSE,
    two_fa_secret BYTEA,  -- Encrypted TOTP secret
    preferences JSONB DEFAULT '{"language": "en", "currency": "USD", "theme": "light"}'::JSONB,
    consent_given BOOLEAN DEFAULT FALSE,  -- GDPR compliance
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE  -- Soft delete support
);

COMMENT ON TABLE users IS 'User accounts with role-based access control and preferences';
COMMENT ON COLUMN users.password IS 'Encrypted with pgCrypto extension';
COMMENT ON COLUMN users.two_fa_secret IS 'Encrypted TOTP secret for 2FA';
COMMENT ON COLUMN users.preferences IS 'User preferences including language, currency, theme';
COMMENT ON COLUMN users.consent_given IS 'GDPR consent tracking';

-- Sessions Table (Authentication)
CREATE TABLE sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(512) NOT NULL,  -- JWT token
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE sessions IS 'User session management for JWT tokens';

-- Role Permissions Table (Authorization)
CREATE TABLE role_permissions (
    role role_enum PRIMARY KEY,
    permissions JSONB NOT NULL  -- Array of permission strings
);

COMMENT ON TABLE role_permissions IS 'Role-based permission definitions';

-- Vendors Table (Core Vendor Management)
CREATE TABLE vendors (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    contact_email VARCHAR(255),
    contact_phone VARCHAR(50),
    address JSONB,  -- {street, city, country, lat, lng}
    category VARCHAR(100),
    certifications JSONB,  -- [{type, expiry, proof_url}]
    performance_score FLOAT DEFAULT 0.0 CHECK (performance_score BETWEEN 0 AND 100),
    status vendor_status_enum DEFAULT 'active',
    custom_fields JSONB DEFAULT '{}'::JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deactivated_at TIMESTAMP WITH TIME ZONE,
    blacklisted_reason TEXT
);

COMMENT ON TABLE vendors IS 'Core vendor profiles with lifecycle management';
COMMENT ON COLUMN vendors.address IS 'JSON structure with address components and coordinates';
COMMENT ON COLUMN vendors.certifications IS 'Array of certification objects with type, expiry, proof';
COMMENT ON COLUMN vendors.custom_fields IS 'Dynamic fields added by administrators';

-- User-Vendor Roles Junction Table (Many-to-Many)
CREATE TABLE user_vendor_roles (
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    vendor_id INTEGER NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    role role_enum NOT NULL DEFAULT 'viewer',
    assigned_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, vendor_id)
);

COMMENT ON TABLE user_vendor_roles IS 'Many-to-many relationship between users and vendors with roles';

-- Vendor History Table (Change Tracking)
CREATE TABLE vendor_histories (
    id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    change_type VARCHAR(100) NOT NULL,
    old_value JSONB,
    new_value JSONB,
    changed_by INTEGER NOT NULL REFERENCES users(id),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE vendor_histories IS 'Tracks changes to vendor records for audit purposes';

-- Documents Table (File Management)
CREATE TABLE documents (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL,  -- 'vendor', 'contract', 'invoice'
    entity_id INTEGER NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    s3_url VARCHAR(512) NOT NULL,
    uploaded_by INTEGER NOT NULL REFERENCES users(id),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE documents IS 'File attachments for various entities';

-- Contracts Table (Contract Management)
CREATE TABLE contracts (
    id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    status contract_status_enum DEFAULT 'draft',
    parties JSONB NOT NULL,  -- [{name, role, signer_email}]
    clauses JSONB,  -- {termination: {notice_period_days, penalties}}
    milestones JSONB,  -- [{description, due_date, status}]
    start_date DATE,
    end_date DATE,
    docusign_envelope_id VARCHAR(100),
    amendments JSONB DEFAULT '[]'::JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE contracts IS 'Contract lifecycle management with multi-party support';
COMMENT ON COLUMN contracts.parties IS 'Array of contract parties with roles and contact info';
COMMENT ON COLUMN contracts.clauses IS 'Contract terms and conditions in structured format';
COMMENT ON COLUMN contracts.milestones IS 'Contract milestones with due dates and status';

-- Templates Table (Contract Templates)
CREATE TABLE templates (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,  -- JSON or HTML template
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE templates IS 'Reusable contract templates';

-- Milestones Table (Contract Milestones - Normalized)
CREATE TABLE milestones (
    id SERIAL PRIMARY KEY,
    contract_id INTEGER NOT NULL REFERENCES contracts(id) ON DELETE CASCADE,
    description TEXT NOT NULL,
    due_date DATE,
    status general_status_enum DEFAULT 'open',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE milestones IS 'Contract milestones with tracking';

-- Invoices Table (Invoice Management)
CREATE TABLE invoices (
    id SERIAL PRIMARY KEY,
    contract_id INTEGER REFERENCES contracts(id) ON DELETE CASCADE,
    vendor_id INTEGER NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    amount DECIMAL(15, 2) NOT NULL CHECK (amount >= 0),
    currency currency_enum DEFAULT 'USD',
    status invoice_status_enum DEFAULT 'draft',
    items JSONB NOT NULL,  -- [{description, quantity, unit_price}]
    taxes DECIMAL(15, 2) DEFAULT 0.00,
    penalties DECIMAL(15, 2) DEFAULT 0.00,
    due_date DATE,
    approved_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE invoices IS 'Invoice management with approval workflow';
COMMENT ON COLUMN invoices.items IS 'Array of invoice line items';
COMMENT ON COLUMN invoices.penalties IS 'Performance-based penalties';

-- Payments Table (Payment Processing)
CREATE TABLE payments (
    id SERIAL PRIMARY KEY,
    invoice_id INTEGER NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
    amount DECIMAL(15, 2) NOT NULL CHECK (amount >= 0),
    currency currency_enum DEFAULT 'USD',
    gateway gateway_enum NOT NULL,
    transaction_id VARCHAR(100) UNIQUE NOT NULL,
    status payment_status_enum DEFAULT 'pending',
    partial_amounts JSONB,  -- [{date, amount}] for partial payments
    conversion_rate DECIMAL(10, 6),  -- For multi-currency
    paid_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE payments IS 'Payment processing with multi-currency support';
COMMENT ON COLUMN payments.partial_amounts IS 'Tracking for partial payment scenarios';

-- KPIs Table (Performance Metrics)
CREATE TABLE kpis (
    id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    type kpi_type_enum NOT NULL,
    score FLOAT NOT NULL CHECK (score BETWEEN 0 AND 100),
    period DATE NOT NULL,  -- Monthly aggregation period
    details JSONB,  -- {actual_delivery_days, expected, quality_metrics}
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE kpis IS 'Vendor performance metrics and scorecards';
COMMENT ON COLUMN kpis.period IS 'Aggregation period (typically monthly)';
COMMENT ON COLUMN kpis.details IS 'Detailed metrics and calculations';

-- Risk Assessments Table (Compliance & Risk)
CREATE TABLE risk_assessments (
    id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
    compliance_type compliance_type_enum NOT NULL,
    risk_score FLOAT NOT NULL CHECK (risk_score BETWEEN 0 AND 100),
    issues JSONB,  -- [{description, severity, status}]
    resolved BOOLEAN DEFAULT FALSE,
    assessed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE risk_assessments IS 'Compliance checks and risk scoring';
COMMENT ON COLUMN risk_assessments.issues IS 'Array of identified compliance issues';

-- Alerts Table (System Alerts)
CREATE TABLE alerts (
    id SERIAL PRIMARY KEY,
    type alert_type_enum NOT NULL,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INTEGER NOT NULL,
    message TEXT NOT NULL,
    status general_status_enum DEFAULT 'open',
    notified_users JSONB,  -- Array of user IDs
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP WITH TIME ZONE
);

COMMENT ON TABLE alerts IS 'System-generated alerts for various conditions';
COMMENT ON COLUMN alerts.notified_users IS 'Array of user IDs who were notified';

-- Disputes Table (Dispute Management)
CREATE TABLE disputes (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL,  -- 'contract' or 'invoice'
    entity_id INTEGER NOT NULL,
    description TEXT NOT NULL,
    evidence JSONB,  -- Array of document IDs or URLs
    status general_status_enum DEFAULT 'open',
    resolved_by INTEGER REFERENCES users(id),
    resolution TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP WITH TIME ZONE
);

COMMENT ON TABLE disputes IS 'Dispute tracking with evidence management';
COMMENT ON COLUMN disputes.evidence IS 'Array of supporting documents and evidence';

-- Reports Table (Custom Reporting)
CREATE TABLE reports (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    config JSONB NOT NULL,  -- {type, filters, schedule}
    generated_by INTEGER NOT NULL REFERENCES users(id),
    last_generated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE reports IS 'Custom report configurations with scheduling';
COMMENT ON COLUMN reports.config IS 'Report configuration including filters and schedule';

-- AI Insights Table (AI/ML Features)
CREATE TABLE ai_insights (
    id SERIAL PRIMARY KEY,
    type ai_insight_type_enum NOT NULL,
    vendor_id INTEGER REFERENCES vendors(id),
    insight_data JSONB NOT NULL,  -- {prediction, explanation, model_version}
    confidence FLOAT CHECK (confidence BETWEEN 0 AND 1),
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE ai_insights IS 'AI-generated insights and predictions';
COMMENT ON COLUMN ai_insights.insight_data IS 'Structured insight data with explanations';
COMMENT ON COLUMN ai_insights.confidence IS 'Model confidence score (0-1)';

-- Workflows Table (Workflow Definitions)
CREATE TABLE workflows (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    steps JSONB NOT NULL,  -- [{type, assignee_role, conditions, escalation_timeout}]
    triggers JSONB,  -- [{event, entity_type}]
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE workflows IS 'Workflow definitions with steps and triggers';
COMMENT ON COLUMN workflows.steps IS 'Array of workflow steps with conditions and assignments';
COMMENT ON COLUMN workflows.triggers IS 'Events that trigger workflow execution';

-- Workflow Instances Table (Workflow Execution)
CREATE TABLE workflow_instances (
    id SERIAL PRIMARY KEY,
    workflow_id INTEGER NOT NULL REFERENCES workflows(id) ON DELETE CASCADE,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INTEGER NOT NULL,
    status workflow_status_enum DEFAULT 'pending',
    current_step INTEGER DEFAULT 1,
    context JSONB,  -- Runtime data and variables
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE
);

COMMENT ON TABLE workflow_instances IS 'Running instances of workflows';
COMMENT ON COLUMN workflow_instances.context IS 'Runtime context and variables';

-- Integrations Table (External Integrations)
CREATE TABLE integrations (
    id SERIAL PRIMARY KEY,
    type integration_type_enum NOT NULL,
    credentials JSONB NOT NULL,  -- Encrypted credentials
    webhook_url VARCHAR(255),
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE integrations IS 'External system integrations';
COMMENT ON COLUMN integrations.credentials IS 'Encrypted API credentials and configuration';

-- Notifications Table (Notification Log)
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    type notification_type_enum NOT NULL,
    recipient VARCHAR(255) NOT NULL,  -- Email or phone number
    content TEXT NOT NULL,
    related_entity_type VARCHAR(50),
    related_entity_id INTEGER,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'sent' CHECK (status IN ('sent', 'failed'))
);

COMMENT ON TABLE notifications IS 'Log of sent notifications for audit purposes';

-- System Settings Table (Configuration)
CREATE TABLE system_settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    updated_by INTEGER NOT NULL REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE system_settings IS 'System-wide configuration settings';

-- Backups Table (Backup Management)
CREATE TABLE backups (
    id SERIAL PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    s3_url VARCHAR(512) NOT NULL,
    hash VARCHAR(64) NOT NULL,  -- SHA256 for integrity
    size_bytes BIGINT NOT NULL,
    created_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE backups IS 'Database backup metadata and integrity tracking';

-- Comments Table (Collaboration)
CREATE TABLE comments (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL,  -- 'vendor', 'contract', 'invoice'
    entity_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    text TEXT NOT NULL,
    parent_comment_id INTEGER REFERENCES comments(id),  -- Threading support
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE comments IS 'Collaborative comments with threading support';
COMMENT ON COLUMN comments.parent_comment_id IS 'Self-reference for comment threading';

-- Audits Table (Audit Trail)
CREATE TABLE audits (
    id SERIAL PRIMARY KEY,
    entity_type VARCHAR(50) NOT NULL,
    entity_id INTEGER NOT NULL,
    action audit_action_enum NOT NULL,
    user_id INTEGER REFERENCES users(id),
    old_value JSONB,
    new_value JSONB,
    details JSONB,  -- {ip, session_id, user_agent}
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE audits IS 'Immutable audit trail for all system changes';
COMMENT ON COLUMN audits.details IS 'Additional context like IP address and session info';

-- Config Logs Table (Configuration Audit)
CREATE TABLE config_logs (
    id SERIAL PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL,
    action audit_action_enum NOT NULL,
    user_id INTEGER NOT NULL REFERENCES users(id),
    old_value JSONB,
    new_value JSONB,
    details JSONB,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE config_logs IS 'Immutable log of configuration changes';

-- ============================================================================
-- 4. INDEXES FOR PERFORMANCE
-- ============================================================================

-- Users indexes
CREATE INDEX idx_users_email_role ON users (email, role);
CREATE INDEX idx_users_created_at ON users (created_at);

-- Sessions indexes
CREATE INDEX idx_sessions_user_id_expires ON sessions (user_id, expires_at);
CREATE INDEX idx_sessions_token ON sessions (token);

-- Vendors indexes (including fuzzy search)
CREATE INDEX idx_vendors_name_trgm ON vendors USING GIN (name gin_trgm_ops);
CREATE INDEX idx_vendors_category ON vendors (category);
CREATE INDEX idx_vendors_performance_score ON vendors (performance_score);
CREATE INDEX idx_vendors_status ON vendors (status);
CREATE INDEX idx_vendors_certifications_gin ON vendors USING GIN (certifications);

-- User-Vendor roles indexes
CREATE INDEX idx_user_vendor_roles_user_id ON user_vendor_roles (user_id);
CREATE INDEX idx_user_vendor_roles_vendor_id ON user_vendor_roles (vendor_id);

-- Vendor histories indexes
CREATE INDEX idx_vendor_histories_vendor_timestamp ON vendor_histories (vendor_id, timestamp);

-- Documents indexes
CREATE INDEX idx_documents_entity ON documents (entity_type, entity_id);
CREATE INDEX idx_documents_uploaded_by ON documents (uploaded_by);

-- Contracts indexes
CREATE INDEX idx_contracts_vendor_status ON contracts (vendor_id, status);
CREATE INDEX idx_contracts_end_date ON contracts (end_date);
CREATE INDEX idx_contracts_status ON contracts (status);

-- Milestones indexes
CREATE INDEX idx_milestones_contract_due ON milestones (contract_id, due_date);

-- Invoices indexes
CREATE INDEX idx_invoices_contract_status ON invoices (contract_id, status);
CREATE INDEX idx_invoices_vendor_id ON invoices (vendor_id);
CREATE INDEX idx_invoices_due_date ON invoices (due_date);
CREATE INDEX idx_invoices_status ON invoices (status);

-- Payments indexes
CREATE INDEX idx_payments_invoice_status ON payments (invoice_id, status);
CREATE INDEX idx_payments_transaction_id ON payments (transaction_id);

-- KPIs indexes
CREATE INDEX idx_kpis_vendor_type_period ON kpis (vendor_id, type, period);

-- Risk assessments indexes
CREATE INDEX idx_risk_assessments_vendor_type ON risk_assessments (vendor_id, compliance_type);
CREATE INDEX idx_risk_assessments_assessed_at ON risk_assessments (assessed_at);

-- Alerts indexes
CREATE INDEX idx_alerts_type_entity ON alerts (type, entity_type, entity_id);
CREATE INDEX idx_alerts_status ON alerts (status);

-- Disputes indexes
CREATE INDEX idx_disputes_entity ON disputes (entity_type, entity_id);
CREATE INDEX idx_disputes_status ON disputes (status);

-- Reports indexes
CREATE INDEX idx_reports_generated_by ON reports (generated_by);

-- AI insights indexes
CREATE INDEX idx_ai_insights_vendor_type ON ai_insights (vendor_id, type);
CREATE INDEX idx_ai_insights_generated_at ON ai_insights (generated_at);

-- Workflow instances indexes
CREATE INDEX idx_workflow_instances_workflow_status ON workflow_instances (workflow_id, status);
CREATE INDEX idx_workflow_instances_entity ON workflow_instances (entity_type, entity_id);

-- Notifications indexes
CREATE INDEX idx_notifications_recipient ON notifications (recipient);
CREATE INDEX idx_notifications_related_entity ON notifications (related_entity_type, related_entity_id);

-- Comments indexes
CREATE INDEX idx_comments_entity ON comments (entity_type, entity_id);
CREATE INDEX idx_comments_user_timestamp ON comments (user_id, created_at);
CREATE INDEX idx_comments_parent ON comments (parent_comment_id);

-- Audits indexes (for querying logs)
CREATE INDEX idx_audits_entity_timestamp ON audits (entity_type, entity_id, timestamp);
CREATE INDEX idx_audits_user_timestamp ON audits (user_id, timestamp);
CREATE INDEX idx_audits_timestamp ON audits (timestamp DESC);

-- Config logs indexes
CREATE INDEX idx_config_logs_setting_timestamp ON config_logs (setting_key, timestamp);
CREATE INDEX idx_config_logs_timestamp ON config_logs (timestamp DESC);

-- ============================================================================
-- 5. FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply updated_at trigger to relevant tables
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_vendors_updated_at BEFORE UPDATE ON vendors FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_contracts_updated_at BEFORE UPDATE ON contracts FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_invoices_updated_at BEFORE UPDATE ON invoices FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_workflows_updated_at BEFORE UPDATE ON workflows FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON comments FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Function to prevent updates/deletes on immutable audit tables
CREATE OR REPLACE FUNCTION prevent_audit_modifications()
RETURNS TRIGGER AS $$
BEGIN
    RAISE EXCEPTION 'Modifications to audit tables are not allowed';
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Apply immutability to audit tables
CREATE TRIGGER prevent_audits_modifications BEFORE UPDATE OR DELETE ON audits FOR EACH ROW EXECUTE PROCEDURE prevent_audit_modifications();
CREATE TRIGGER prevent_config_logs_modifications BEFORE UPDATE OR DELETE ON config_logs FOR EACH ROW EXECUTE PROCEDURE prevent_audit_modifications();

-- Function to automatically log vendor changes
CREATE OR REPLACE FUNCTION log_vendor_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' THEN
        INSERT INTO audits (entity_type, entity_id, action, user_id, old_value, new_value, timestamp)
        VALUES ('vendors', NEW.id, 'update', COALESCE(CURRENT_SETTING('app.current_user_id', true)::INTEGER, 1), row_to_json(OLD)::JSONB, row_to_json(NEW)::JSONB, CURRENT_TIMESTAMP);
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audits (entity_type, entity_id, action, user_id, new_value, timestamp)
        VALUES ('vendors', NEW.id, 'create', COALESCE(CURRENT_SETTING('app.current_user_id', true)::INTEGER, 1), row_to_json(NEW)::JSONB, CURRENT_TIMESTAMP);
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audits (entity_type, entity_id, action, user_id, old_value, timestamp)
        VALUES ('vendors', OLD.id, 'delete', COALESCE(CURRENT_SETTING('app.current_user_id', true)::INTEGER, 1), row_to_json(OLD)::JSONB, CURRENT_TIMESTAMP);
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Apply audit trigger to vendors table
CREATE TRIGGER vendor_audit_trigger AFTER INSERT OR UPDATE OR DELETE ON vendors FOR EACH ROW EXECUTE PROCEDURE log_vendor_changes();

-- ============================================================================
-- 6. VIEWS AND MATERIALIZED VIEWS
-- ============================================================================

-- View: Vendor Performance Summary
CREATE VIEW vendor_performance_summary AS
SELECT 
    v.id,
    v.name,
    v.performance_score,
    v.status,
    AVG(k.score) as avg_kpi_score,
    COUNT(DISTINCT k.type) as kpi_types_covered,
    COUNT(DISTINCT c.id) as total_contracts,
    COUNT(DISTINCT CASE WHEN c.status = 'active' THEN c.id END) as active_contracts,
    COALESCE(SUM(i.amount), 0) as total_spend,
    COUNT(DISTINCT ra.id) as risk_assessments_count,
    AVG(ra.risk_score) as avg_risk_score
FROM vendors v
LEFT JOIN kpis k ON v.id = k.vendor_id
LEFT JOIN contracts c ON v.id = c.vendor_id
LEFT JOIN invoices i ON v.id = i.vendor_id AND i.status = 'paid'
LEFT JOIN risk_assessments ra ON v.id = ra.vendor_id
WHERE v.status = 'active'
GROUP BY v.id, v.name, v.performance_score, v.status;

COMMENT ON VIEW vendor_performance_summary IS 'Comprehensive vendor performance overview';

-- Materialized View: Monthly Spend Analysis
CREATE MATERIALIZED VIEW monthly_spend_analysis AS
SELECT 
    DATE_TRUNC('month', i.created_at) as month,
    v.id as vendor_id,
    v.name as vendor_name,
    v.category,
    COUNT(i.id) as invoice_count,
    SUM(i.amount) as total_amount,
    AVG(i.amount) as avg_invoice_amount,
    i.currency
FROM invoices i
JOIN vendors v ON i.vendor_id = v.id
WHERE i.status IN ('paid', 'partially_paid')
GROUP BY DATE_TRUNC('month', i.created_at), v.id, v.name, v.category, i.currency
ORDER BY month DESC, total_amount DESC;

COMMENT ON MATERIALIZED VIEW monthly_spend_analysis IS 'Monthly spending analysis by vendor (refresh periodically)';

-- ============================================================================
-- 7. ROW LEVEL SECURITY (RLS) POLICIES
-- ============================================================================

-- Enable RLS on sensitive tables
ALTER TABLE audits ENABLE ROW LEVEL SECURITY;
ALTER TABLE config_logs ENABLE ROW LEVEL SECURITY;

-- Policy: Only admins can view audit logs
CREATE POLICY admin_audit_access ON audits
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.id = CURRENT_SETTING('app.current_user_id', true)::INTEGER 
        AND users.role = 'admin'
    )
);

-- Policy: Only admins can view config logs
CREATE POLICY admin_config_logs_access ON config_logs
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM users 
        WHERE users.id = CURRENT_SETTING('app.current_user_id', true)::INTEGER 
        AND users.role = 'admin'
    )
);

-- ============================================================================
-- 8. INITIAL DATA SEEDING
-- ============================================================================

-- Insert default role permissions
INSERT INTO role_permissions (role, permissions) VALUES
('admin', '["*"]'::JSONB),
('manager', '["vendors:read", "vendors:write", "contracts:read", "contracts:write", "contracts:approve", "invoices:read", "invoices:write", "invoices:approve", "reports:read", "reports:write"]'::JSONB),
('viewer', '["vendors:read", "contracts:read", "invoices:read", "reports:read"]'::JSONB);

-- Insert default system settings
INSERT INTO system_settings (key, value, updated_by) VALUES
('theme_primary_color', '"#3b82f6"'::JSONB, 1),
('theme_secondary_color', '"#64748b"'::JSONB, 1),
('default_currency', '"USD"'::JSONB, 1),
('default_language', '"en"'::JSONB, 1),
('invoice_approval_required', 'true'::JSONB, 1),
('contract_esignature_enabled', 'true'::JSONB, 1);

-- Create default admin user (password should be changed immediately)
INSERT INTO users (email, password, role, is_verified, consent_given) VALUES
('<EMAIL>', pgp_sym_encrypt('admin123', 'default_key'), 'admin', true, true);

-- ============================================================================
-- 9. UTILITY FUNCTIONS
-- ============================================================================

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION refresh_analytics_views()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW monthly_spend_analysis;
    -- Add other materialized views here as they are created
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION refresh_analytics_views() IS 'Refresh all materialized views for analytics';

-- Function to get vendor performance score
CREATE OR REPLACE FUNCTION calculate_vendor_performance_score(vendor_id_param INTEGER)
RETURNS FLOAT AS $$
DECLARE
    avg_kpi_score FLOAT;
    risk_penalty FLOAT;
    final_score FLOAT;
BEGIN
    -- Calculate average KPI score
    SELECT AVG(score) INTO avg_kpi_score
    FROM kpis 
    WHERE vendor_id = vendor_id_param 
    AND period >= CURRENT_DATE - INTERVAL '6 months';
    
    -- Calculate risk penalty
    SELECT AVG(risk_score) INTO risk_penalty
    FROM risk_assessments 
    WHERE vendor_id = vendor_id_param 
    AND NOT resolved;
    
    -- Calculate final score
    final_score := COALESCE(avg_kpi_score, 0) - COALESCE(risk_penalty * 0.1, 0);
    
    -- Ensure score is between 0 and 100
    final_score := GREATEST(0, LEAST(100, final_score));
    
    RETURN final_score;
END;
$$ LANGUAGE plpgsql;

COMMENT ON FUNCTION calculate_vendor_performance_score(INTEGER) IS 'Calculate comprehensive vendor performance score';

-- ============================================================================
-- SCHEMA CREATION COMPLETE
-- ============================================================================

-- Grant permissions to application user (adjust as needed)
-- GRANT USAGE ON SCHEMA public TO vendorms_app;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO vendorms_app;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO vendorms_app;

-- Final message
SELECT 'VendorMS database schema created successfully!' as status;