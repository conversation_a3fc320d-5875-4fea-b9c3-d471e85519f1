import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Provider } from 'react-redux';
import { store } from './store';

// Components
import { ProtectedRoute } from './components/ProtectedRoute';
import { AuthRedirect } from './components/auth/AuthRedirect';
import { AppLayout } from './components/Layout/AppLayout';

// Pages
import { Login } from './pages/Login';
import { Register } from './pages/Register';
import { ForgotPassword } from './pages/ForgotPassword';
import { ResetPassword } from './pages/ResetPassword';
import { VerifyEmail } from './pages/VerifyEmail';
import { VerificationPending } from './pages/VerificationPending';
import { Dashboard } from './pages/Dashboard';
import { VendorsList } from './pages/VendorsList';
import { VendorCreate } from './pages/VendorCreate';
import { VendorView } from './pages/VendorView';
import { VendorEdit } from './pages/VendorEdit';
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={
              <AuthRedirect>
                <Login />
              </AuthRedirect>
            } />
            <Route path="/register" element={
              <AuthRedirect>
                <Register />
              </AuthRedirect>
            } />
            <Route path="/forgot-password" element={
              <AuthRedirect>
                <ForgotPassword />
              </AuthRedirect>
            } />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/verify-email" element={<VerifyEmail />} />
            <Route path="/verification-pending" element={
              <AuthRedirect>
                <VerificationPending />
              </AuthRedirect>
            } />
            
            {/* Protected Routes */}
            <Route path="/" element={
              <ProtectedRoute>
                <AppLayout />
              </ProtectedRoute>
            }>
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="dashboard" element={<Dashboard />} />
              
              {/* Vendor Routes */}
              <Route path="vendors">
                <Route path="list" element={<VendorsList />} />
                <Route path="create" element={
                  <ProtectedRoute requiredRole="manager">
                    <VendorCreate />
                  </ProtectedRoute>
                } />
                <Route path=":id" element={<VendorView />} />
                <Route path=":id/edit" element={
                  <ProtectedRoute requiredRole="manager">
                    <VendorEdit />
                  </ProtectedRoute>
                } />
                <Route index element={<Navigate to="/vendors/list" replace />} />
              </Route>
              
              {/* Contract Routes */}
              <Route path="contracts">
                <Route path="list" element={
                  <div className="p-8 text-center">Contracts List (Coming Soon)</div>
                } />
                <Route path="create" element={
                  <ProtectedRoute requiredRole="manager">
                    <div className="p-8 text-center">Create Contract (Coming Soon)</div>
                  </ProtectedRoute>
                } />
                <Route index element={<Navigate to="/contracts/list" replace />} />
              </Route>
              
              {/* Invoice Routes */}
              <Route path="invoices">
                <Route path="list" element={
                  <div className="p-8 text-center">Invoices List (Coming Soon)</div>
                } />
                <Route path="generate" element={
                  <ProtectedRoute requiredRole="manager">
                    <div className="p-8 text-center">Generate Invoice (Coming Soon)</div>
                  </ProtectedRoute>
                } />
                <Route index element={<Navigate to="/invoices/list" replace />} />
              </Route>
              
              {/* Performance Routes */}
              <Route path="performance">
                <Route path="scorecards" element={
                  <div className="p-8 text-center">Performance Scorecards (Coming Soon)</div>
                } />
                <Route path="risks" element={
                  <div className="p-8 text-center">Risk Assessment (Coming Soon)</div>
                } />
                <Route index element={<Navigate to="/performance/scorecards" replace />} />
              </Route>
              
              {/* Analytics Routes */}
              <Route path="analytics">
                <Route path="dashboard" element={
                  <div className="p-8 text-center">Analytics Dashboard (Coming Soon)</div>
                } />
                <Route path="reports" element={
                  <div className="p-8 text-center">Custom Reports (Coming Soon)</div>
                } />
                <Route path="ai-insights" element={
                  <div className="p-8 text-center">AI Insights (Coming Soon)</div>
                } />
                <Route index element={<Navigate to="/analytics/dashboard" replace />} />
              </Route>
              
              {/* Workflow Routes */}
              <Route path="workflows">
                <Route path="list" element={
                  <ProtectedRoute requiredRole="manager">
                    <div className="p-8 text-center">Workflows List (Coming Soon)</div>
                  </ProtectedRoute>
                } />
                <Route path="create" element={
                  <ProtectedRoute requiredRole="admin">
                    <div className="p-8 text-center">Create Workflow (Coming Soon)</div>
                  </ProtectedRoute>
                } />
                <Route index element={<Navigate to="/workflows/list" replace />} />
              </Route>
              
              {/* Admin Routes */}
              <Route path="admin">
                <Route path="users" element={
                  <ProtectedRoute requiredRole="admin">
                    <div className="p-8 text-center">User Management (Coming Soon)</div>
                  </ProtectedRoute>
                } />
                <Route path="settings" element={
                  <ProtectedRoute requiredRole="admin">
                    <div className="p-8 text-center">System Settings (Coming Soon)</div>
                  </ProtectedRoute>
                } />
                <Route path="backup" element={
                  <ProtectedRoute requiredRole="admin">
                    <div className="p-8 text-center">Backup Management (Coming Soon)</div>
                  </ProtectedRoute>
                } />
                <Route index element={<Navigate to="/admin/users" replace />} />
              </Route>
            </Route>
            
            {/* Fallback Routes */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  </Provider>
);

export default App;
