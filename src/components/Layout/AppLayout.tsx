import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Outlet } from 'react-router-dom';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { RootState, AppDispatch } from '../../store';
import { setTheme } from '../../store/slices/uiSlice';

export const AppLayout: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { theme, sidebarCollapsed } = useSelector((state: RootState) => state.ui);

  useEffect(() => {
    // Apply theme on mount
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [theme]);

  return (
    <div className="min-h-screen bg-background transition-smooth">
      <div className="flex w-full">
        <Sidebar />
        
        <div className={`flex-1 transition-all duration-300 ${
          sidebarCollapsed ? 'ml-16' : 'ml-64'
        }`}>
          <Header />
          
          <main className="p-6">
            <div className="max-w-7xl mx-auto">
              <Outlet />
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};