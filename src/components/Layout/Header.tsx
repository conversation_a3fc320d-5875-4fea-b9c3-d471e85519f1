import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { 
  Bell, 
  Search, 
  Sun, 
  Moon, 
  Globe, 
  DollarSign,
  User,
  LogOut,
  Settings
} from 'lucide-react';
import { RootState, AppDispatch } from '../../store';
import { setTheme, setLanguage, setCurrency } from '../../store/slices/uiSlice';
import { logoutAsync } from '../../store/slices/authSlice';
import { markAsRead } from '../../store/slices/notificationsSlice';
import { useAuth } from '../../hooks/useAuth';

export const Header: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { theme, language, currency } = useSelector((state: RootState) => state.ui);
  const { notifications, unreadCount } = useSelector((state: RootState) => state.notifications);

  const handleThemeToggle = () => {
    dispatch(setTheme(theme === 'light' ? 'dark' : 'light'));
  };

  const handleLanguageChange = (lang: 'en' | 'es' | 'fr') => {
    dispatch(setLanguage(lang));
  };

  const handleCurrencyChange = (curr: 'USD' | 'EUR' | 'GBP') => {
    dispatch(setCurrency(curr));
  };

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {
    dispatch(markAsRead(notificationId));
    if (actionUrl) {
      navigate(actionUrl);
    }
  };

  return (
    <header className="h-16 bg-card border-b border-border px-6 flex items-center justify-between">
      {/* Search */}
      <div className="flex-1 max-w-md">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <input
            type="text"
            placeholder="Search vendors, contracts, invoices..."
            className="input-neumorphic w-full pl-10 pr-4"
          />
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center space-x-4">
        {/* Language Selector */}
        <div className="relative group">
          <button className="btn-neumorphic p-2">
            <Globe className="w-4 h-4" />
          </button>
          <div className="absolute right-0 top-full mt-2 w-32 bg-popover border border-border rounded-xl shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
            {['en', 'es', 'fr'].map((lang) => (
              <button
                key={lang}
                onClick={() => handleLanguageChange(lang as any)}
                className={`w-full px-3 py-2 text-left hover:bg-accent rounded-xl transition-colors ${
                  language === lang ? 'bg-accent text-accent-foreground' : ''
                }`}
              >
                {lang.toUpperCase()}
              </button>
            ))}
          </div>
        </div>

        {/* Currency Selector */}
        <div className="relative group">
          <button className="btn-neumorphic p-2">
            <DollarSign className="w-4 h-4" />
          </button>
          <div className="absolute right-0 top-full mt-2 w-32 bg-popover border border-border rounded-xl shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
            {['USD', 'EUR', 'GBP'].map((curr) => (
              <button
                key={curr}
                onClick={() => handleCurrencyChange(curr as any)}
                className={`w-full px-3 py-2 text-left hover:bg-accent rounded-xl transition-colors ${
                  currency === curr ? 'bg-accent text-accent-foreground' : ''
                }`}
              >
                {curr}
              </button>
            ))}
          </div>
        </div>

        {/* Theme Toggle */}
        <button
          onClick={handleThemeToggle}
          className="btn-neumorphic p-2"
        >
          {theme === 'light' ? (
            <Moon className="w-4 h-4" />
          ) : (
            <Sun className="w-4 h-4" />
          )}
        </button>

        {/* Notifications */}
        <div className="relative group">
          <button className="btn-neumorphic p-2 relative">
            <Bell className="w-4 h-4" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-destructive text-destructive-foreground text-xs rounded-full w-5 h-5 flex items-center justify-center">
                {unreadCount}
              </span>
            )}
          </button>
          
          <div className="absolute right-0 top-full mt-2 w-80 bg-popover border border-border rounded-xl shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
            <div className="p-4 border-b border-border">
              <h3 className="font-medium">Notifications</h3>
            </div>
            <div className="max-h-64 overflow-y-auto">
              {notifications.slice(0, 5).map((notification) => (
                <button
                  key={notification.id}
                  onClick={() => handleNotificationClick(notification.id, notification.actionUrl)}
                  className={`w-full p-3 text-left hover:bg-accent transition-colors border-b border-border last:border-b-0 ${
                    !notification.read ? 'bg-primary/5' : ''
                  }`}
                >
                  <div className="flex items-start space-x-2">
                    <div className={`w-2 h-2 rounded-full mt-2 ${
                      notification.type === 'info' ? 'bg-info' :
                      notification.type === 'success' ? 'bg-success' :
                      notification.type === 'warning' ? 'bg-warning' :
                      'bg-destructive'
                    }`} />
                    <div className="flex-1">
                      <p className="font-medium text-sm">{notification.title}</p>
                      <p className="text-xs text-muted-foreground">{notification.message}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {new Date(notification.timestamp).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* User Menu */}
        <div className="relative group">
          <button className="btn-neumorphic p-2 flex items-center space-x-2">
            <User className="w-4 h-4" />
            <span className="text-sm font-medium">{user?.email?.split('@')[0]}</span>
          </button>
          
          <div className="absolute right-0 top-full mt-2 w-48 bg-popover border border-border rounded-xl shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
            <div className="p-2">
              <div className="px-3 py-2 border-b border-border">
                <p className="text-sm font-medium">{user?.email}</p>
                <p className="text-xs text-muted-foreground capitalize">{user?.role}</p>
              </div>
              
              <button
                onClick={() => navigate('/profile')}
                className="w-full px-3 py-2 text-left hover:bg-accent rounded-xl transition-colors flex items-center space-x-2"
              >
                <Settings className="w-4 h-4" />
                <span>Profile Settings</span>
              </button>
              
              <button
                onClick={handleLogout}
                className="w-full px-3 py-2 text-left hover:bg-accent rounded-xl transition-colors flex items-center space-x-2 text-destructive"
              >
                <LogOut className="w-4 h-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};