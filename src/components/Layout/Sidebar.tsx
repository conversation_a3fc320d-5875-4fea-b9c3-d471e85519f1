import React from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { 
  LayoutDashboard, 
  Users, 
  FileText, 
  Receipt, 
  BarChart3, 
  Settings, 
  Building2,
  GitBranch,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { RootState, AppDispatch } from '../../store';
import { toggleSidebar } from '../../store/slices/uiSlice';
import { useAuth } from '../../hooks/useAuth';

interface NavigationItem {
  name: string;
  path: string;
  icon: React.ComponentType<{ className?: string }>;
  requiredRole?: 'admin' | 'manager' | 'viewer';
  children?: NavigationItem[];
}

const navigationItems: NavigationItem[] = [
  {
    name: 'Dashboard',
    path: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    name: 'Vendors',
    path: '/vendors',
    icon: Building2,
    children: [
      { name: 'All Vendors', path: '/vendors/list', icon: Users },
      { name: 'Add Vendor', path: '/vendors/create', icon: Users, requiredRole: 'manager' },
    ],
  },
  {
    name: 'Contracts',
    path: '/contracts',
    icon: FileText,
    children: [
      { name: 'All Contracts', path: '/contracts/list', icon: FileText },
      { name: 'Create Contract', path: '/contracts/create', icon: FileText, requiredRole: 'manager' },
    ],
  },
  {
    name: 'Invoices',
    path: '/invoices',
    icon: Receipt,
    children: [
      { name: 'All Invoices', path: '/invoices/list', icon: Receipt },
      { name: 'Generate Invoice', path: '/invoices/generate', icon: Receipt, requiredRole: 'manager' },
    ],
  },
  {
    name: 'Performance',
    path: '/performance',
    icon: BarChart3,
    children: [
      { name: 'Scorecards', path: '/performance/scorecards', icon: BarChart3 },
      { name: 'Risk Assessment', path: '/performance/risks', icon: BarChart3 },
    ],
  },
  {
    name: 'Analytics',
    path: '/analytics',
    icon: BarChart3,
    children: [
      { name: 'Dashboard', path: '/analytics/dashboard', icon: BarChart3 },
      { name: 'Custom Reports', path: '/analytics/reports', icon: BarChart3 },
      { name: 'AI Insights', path: '/analytics/ai-insights', icon: BarChart3 },
    ],
  },
  {
    name: 'Workflows',
    path: '/workflows',
    icon: GitBranch,
    requiredRole: 'manager',
    children: [
      { name: 'All Workflows', path: '/workflows/list', icon: GitBranch },
      { name: 'Create Workflow', path: '/workflows/create', icon: GitBranch, requiredRole: 'admin' },
    ],
  },
  {
    name: 'Admin',
    path: '/admin',
    icon: Settings,
    requiredRole: 'admin',
    children: [
      { name: 'Users', path: '/admin/users', icon: Users },
      { name: 'Settings', path: '/admin/settings', icon: Settings },
      { name: 'Backup', path: '/admin/backup', icon: Settings },
    ],
  },
];

export const Sidebar: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { sidebarCollapsed } = useSelector((state: RootState) => state.ui);
  const { hasPermission } = useAuth();
  const location = useLocation();

  const isActiveRoute = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  const renderNavItem = (item: NavigationItem, level = 0) => {
    if (item.requiredRole && !hasPermission(item.requiredRole)) {
      return null;
    }

    const hasChildren = item.children && item.children.length > 0;
    const isActive = isActiveRoute(item.path);
    const Icon = item.icon;

    if (hasChildren) {
      return (
        <div key={item.path} className="mb-1">
          <div className={`flex items-center px-3 py-2 rounded-xl transition-all duration-200 ${
            isActive ? 'bg-primary text-primary-foreground soft-shadow' : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
          }`}>
            <Icon className={`w-5 h-5 ${sidebarCollapsed ? '' : 'mr-3'}`} />
            {!sidebarCollapsed && (
              <span className="font-medium">{item.name}</span>
            )}
          </div>
          
          {!sidebarCollapsed && (
            <div className="ml-4 mt-1 space-y-1">
              {item.children.map(child => renderNavItem(child, level + 1))}
            </div>
          )}
        </div>
      );
    }

    return (
      <NavLink
        key={item.path}
        to={item.path}
        className={({ isActive }) => `
          flex items-center px-3 py-2 rounded-xl transition-all duration-200 mb-1
          ${isActive 
            ? 'bg-primary text-primary-foreground soft-shadow' 
            : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground hover:scale-105'
          }
        `}
      >
        <Icon className={`w-5 h-5 ${sidebarCollapsed ? '' : 'mr-3'}`} />
        {!sidebarCollapsed && (
          <span className="font-medium">{item.name}</span>
        )}
      </NavLink>
    );
  };

  return (
    <div className={`fixed left-0 top-0 h-full bg-card border-r border-border transition-all duration-300 z-30 ${
      sidebarCollapsed ? 'w-16' : 'w-64'
    }`}>
      <div className="p-4 flex flex-col h-full">
        {/* Logo */}
        <div className="flex items-center mb-8">
          <div className="w-8 h-8 bg-primary rounded-lg gradient-primary soft-shadow flex items-center justify-center">
            <Building2 className="w-5 h-5 text-primary-foreground" />
          </div>
          {!sidebarCollapsed && (
            <div className="ml-3">
              <h1 className="text-lg font-bold text-foreground">VendorMS</h1>
              <p className="text-xs text-muted-foreground">Management System</p>
            </div>
          )}
        </div>

        {/* Toggle Button */}
        <button
          onClick={() => dispatch(toggleSidebar())}
          className="btn-neumorphic mb-6 w-full flex items-center justify-center"
        >
          {sidebarCollapsed ? (
            <ChevronRight className="w-4 h-4" />
          ) : (
            <ChevronLeft className="w-4 h-4" />
          )}
        </button>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto space-y-1">
          {navigationItems.map(item => renderNavItem(item))}
        </nav>
      </div>
    </div>
  );
};