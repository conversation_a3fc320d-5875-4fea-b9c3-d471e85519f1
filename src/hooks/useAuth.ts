import { useSelector, useDispatch } from 'react-redux';
import { useEffect } from 'react';
import { RootState, AppDispatch } from '../store';
import { 
  loginAsync, 
  logoutAsync, 
  verifyTokenAsync, 
  registerAsync, 
  resendVerificationAsync,
  forgotPasswordAsync,
  resetPasswordAsync,
  verifyEmailAsync,
  RegisterData,
  ForgotPasswordData,
  ResetPasswordData 
} from '../store/slices/authSlice';

export const useAuth = () => {
  const dispatch = useDispatch<AppDispatch>();
  const auth = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    const token = localStorage.getItem('vms_token');
    if (token && !auth.isAuthenticated && !auth.isLoading) {
      dispatch(verifyTokenAsync());
    }
  }, [dispatch, auth.isAuthenticated, auth.isLoading]);

  const login = async (email: string, password: string) => {
    return dispatch(loginAsync({ email, password }));
  };

  const logout = async () => {
    return dispatch(logoutAsync());
  };

  const register = async (data: RegisterData) => {
    return dispatch(registerAsync(data));
  };

  const resendVerification = async (email: string) => {
    return dispatch(resendVerificationAsync(email));
  };

  const forgotPassword = async (data: ForgotPasswordData) => {
    return dispatch(forgotPasswordAsync(data));
  };

  const resetPassword = async (data: ResetPasswordData) => {
    return dispatch(resetPasswordAsync(data));
  };

  const verifyEmail = async (token: string) => {
    return dispatch(verifyEmailAsync(token));
  };

  const hasPermission = (requiredRole: 'admin' | 'manager' | 'viewer'): boolean => {
    if (!auth.user) return false;
    
    const roleHierarchy = { admin: 3, manager: 2, viewer: 1 };
    const userRoleLevel = roleHierarchy[auth.user.role];
    const requiredRoleLevel = roleHierarchy[requiredRole];
    
    return userRoleLevel >= requiredRoleLevel;
  };

  const canEdit = (): boolean => {
    return hasPermission('manager');
  };

  const canDelete = (): boolean => {
    return hasPermission('admin');
  };

  const canApprove = (): boolean => {
    return hasPermission('manager');
  };

  return {
    ...auth,
    login,
    logout,
    register,
    resendVerification,
    forgotPassword,
    resetPassword,
    verifyEmail,
    hasPermission,
    canEdit,
    canDelete,
    canApprove,
  };
};