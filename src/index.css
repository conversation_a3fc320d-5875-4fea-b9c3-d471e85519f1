@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern VMS Design System with Neumorphism & Soft UI */

@layer base {
  :root {
    /* Primary Brand Colors */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 217 91% 70%;
    --primary-dark: 217 91% 50%;
    
    /* Secondary & Support Colors */
    --secondary: 220 13% 91%;
    --secondary-foreground: 220 9% 46%;
    --accent: 268 83% 58%;
    --accent-foreground: 0 0% 100%;
    
    /* Neutral System */
    --background: 220 20% 97%;
    --background-soft: 220 20% 99%;
    --foreground: 220 13% 18%;
    --muted: 220 13% 91%;
    --muted-foreground: 220 9% 46%;
    
    /* Card System with Neumorphism */
    --card: 220 20% 97%;
    --card-foreground: 220 13% 18%;
    --card-elevated: 0 0% 100%;
    --card-sunken: 220 20% 94%;
    
    /* Interactive Elements */
    --border: 220 13% 87%;
    --border-light: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 217 91% 60%;
    
    /* Status Colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --info: 199 89% 48%;
    --info-foreground: 0 0% 100%;
    
    /* Popover & Overlays */
    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;
    
    /* Sidebar Specific */
    --sidebar-background: 220 20% 99%;
    --sidebar-foreground: 220 13% 25%;
    --sidebar-primary: 217 91% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 220 13% 93%;
    --sidebar-accent-foreground: 220 13% 25%;
    --sidebar-border: 220 13% 87%;
    --sidebar-ring: 217 91% 60%;
    
    /* Neumorphism Shadows */
    --shadow-neumorphic: 
      inset 5px 5px 10px hsl(220 20% 90%),
      inset -5px -5px 10px hsl(0 0% 100%);
    --shadow-elevated: 
      5px 5px 15px hsl(220 20% 85%),
      -5px -5px 15px hsl(0 0% 100%);
    --shadow-soft: 
      3px 3px 8px hsl(220 20% 88%),
      -3px -3px 8px hsl(0 0% 100%);
    --shadow-pressed: 
      inset 3px 3px 8px hsl(220 20% 88%),
      inset -3px -3px 8px hsl(0 0% 100%);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-soft: linear-gradient(135deg, hsl(var(--background)), hsl(var(--background-soft)));
    --gradient-card: linear-gradient(145deg, hsl(var(--card-elevated)), hsl(var(--card)));
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    --radius: 0.75rem;
  }

  .dark {
    /* Dark Mode Adjustments */
    --primary: 217 91% 65%;
    --primary-foreground: 220 13% 18%;
    --primary-glow: 217 91% 75%;
    --primary-dark: 217 91% 55%;
    
    --background: 220 13% 12%;
    --background-soft: 220 13% 14%;
    --foreground: 220 13% 93%;
    --muted: 220 13% 18%;
    --muted-foreground: 220 9% 64%;
    
    --card: 220 13% 14%;
    --card-foreground: 220 13% 93%;
    --card-elevated: 220 13% 16%;
    --card-sunken: 220 13% 10%;
    
    --border: 220 13% 20%;
    --border-light: 220 13% 25%;
    --input: 220 13% 18%;
    
    --secondary: 220 13% 18%;
    --secondary-foreground: 220 13% 93%;
    
    --popover: 220 13% 14%;
    --popover-foreground: 220 13% 93%;
    
    --sidebar-background: 220 13% 10%;
    --sidebar-foreground: 220 13% 85%;
    --sidebar-primary: 217 91% 65%;
    --sidebar-primary-foreground: 220 13% 18%;
    --sidebar-accent: 220 13% 16%;
    --sidebar-accent-foreground: 220 13% 85%;
    --sidebar-border: 220 13% 20%;
    
    /* Dark Neumorphism Shadows */
    --shadow-neumorphic: 
      inset 3px 3px 8px hsl(220 13% 8%),
      inset -3px -3px 8px hsl(220 13% 18%);
    --shadow-elevated: 
      3px 3px 12px hsl(220 13% 6%),
      -3px -3px 12px hsl(220 13% 16%);
    --shadow-soft: 
      2px 2px 6px hsl(220 13% 8%),
      -2px -2px 6px hsl(220 13% 16%);
    --shadow-pressed: 
      inset 2px 2px 6px hsl(220 13% 8%),
      inset -2px -2px 6px hsl(220 13% 16%);
  }
}

@layer base {
  * {
    @apply border-border transition-smooth;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  /* Neumorphism Utility Classes */
  .neumorphic {
    box-shadow: var(--shadow-neumorphic);
  }
  
  .elevated {
    box-shadow: var(--shadow-elevated);
  }
  
  .soft-shadow {
    box-shadow: var(--shadow-soft);
  }
  
  .pressed {
    box-shadow: var(--shadow-pressed);
  }
  
  .gradient-primary {
    background: var(--gradient-primary);
  }
  
  .gradient-soft {
    background: var(--gradient-soft);
  }
  
  .gradient-card {
    background: var(--gradient-card);
  }
  
  .transition-smooth {
    transition: var(--transition-smooth);
  }
  
  .transition-bounce {
    transition: var(--transition-bounce);
  }

  /* Scrollbar Styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-muted rounded-full;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-border/80;
  }
}

@layer components {
  /* Interactive Elements */
  .btn-neumorphic {
    @apply rounded-xl px-4 py-2 font-medium transition-all duration-200;
    @apply bg-card text-card-foreground;
    box-shadow: var(--shadow-soft);
  }
  
  .btn-neumorphic:hover {
    @apply scale-105;
    box-shadow: var(--shadow-elevated);
  }
  
  .btn-neumorphic:active {
    @apply scale-95;
    box-shadow: var(--shadow-pressed);
  }
  
  .card-neumorphic {
    @apply rounded-2xl p-6 transition-all duration-300;
    @apply bg-card text-card-foreground;
    box-shadow: var(--shadow-soft);
  }
  
  .card-neumorphic:hover {
    box-shadow: var(--shadow-elevated);
  }
  
  .input-neumorphic {
    @apply rounded-xl px-4 py-3 transition-all duration-200;
    @apply bg-card text-card-foreground;
    box-shadow: var(--shadow-neumorphic);
    border: 1px solid hsl(var(--border));
  }
  
  .input-neumorphic:focus {
    @apply outline-none ring-2 ring-primary/20;
    box-shadow: var(--shadow-soft);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}