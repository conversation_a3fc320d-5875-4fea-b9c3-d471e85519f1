import { useEffect, useState } from 'react';
import { use<PERSON><PERSON>, Controller, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { ArrowLeft, FileText, Building2, Calendar, Users, AlertTriangle, CheckCircle, Plus, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { fetchVendorsAsync } from '@/store/slices/vendorsSlice';
import { createContractAsync, fetchContractTemplatesAsync } from '@/store/slices/contractsSlice';
import { RootState, AppDispatch } from '@/store';
import { useAuth } from '@/hooks/useAuth';
import { toast } from '@/components/ui/sonner';

const milestoneSchema = yup.object({
  name: yup.string().required('Milestone name is required'),
  description: yup.string().optional(),
  due_date: yup.date().required('Due date is required'),
});

const schema = yup.object({
  title: yup.string().required('Contract title is required'),
  vendor_id: yup.number().required('Vendor selection is required'),
  start_date: yup.date().required('Start date is required'),
  end_date: yup.date()
    .required('End date is required')
    .min(yup.ref('start_date'), 'End date must be after start date'),
  payment_terms: yup.string().required('Payment terms are required'),
  deliverables: yup.string().required('Deliverables description is required'),
  value: yup.number().positive('Contract value must be positive').optional(),
  currency: yup.string().optional(),
  additional_clauses: yup.string().optional(),
  milestones: yup.array().of(milestoneSchema).optional(),
});

type FormData = yup.InferType<typeof schema>;



export default function ContractCreate() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [searchParams] = useSearchParams();
  const { canEdit } = useAuth();

  const { vendors } = useSelector((state: RootState) => state.vendors);
  const { templates } = useSelector((state: RootState) => state.contracts);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [isDraftSaving, setIsDraftSaving] = useState(false);

  const {
    control,
    handleSubmit,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      title: '',
      vendor_id: undefined,
      start_date: undefined,
      end_date: undefined,
      payment_terms: '',
      deliverables: '',
      value: undefined,
      currency: 'USD',
      additional_clauses: '',
      milestones: [],
    },
  });

  const { fields: milestoneFields, append: appendMilestone, remove: removeMilestone } = useFieldArray({
    control,
    name: 'milestones',
  });

  useEffect(() => {
    dispatch(fetchVendorsAsync({}));
    dispatch(fetchContractTemplatesAsync());
  }, [dispatch]);

  // Pre-fill vendor if provided in URL
  useEffect(() => {
    const vendorId = searchParams.get('vendorId');
    if (vendorId) {
      setValue('vendor_id', parseInt(vendorId), { shouldDirty: true });
    }
  }, [searchParams, setValue]);

  if (!canEdit()) {
    return (
      <div className="min-h-screen bg-gradient-subtle flex items-center justify-center">
        <Card className="card-neumorphic max-w-md">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-destructive mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-muted-foreground mb-4">
              You don't have permission to create contracts.
            </p>
            <Button onClick={() => navigate('/contracts/list')}>
              View Contracts
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setValue('title', template.name);
      setValue('payment_terms', template.defaultFields.payment_terms);
      setValue('deliverables', template.defaultFields.deliverables);
      setValue('additional_clauses', template.defaultFields.additional_clauses);
    }
  };

  const addMilestone = () => {
    appendMilestone({
      name: '',
      description: '',
      due_date: undefined,
    });
  };

  const saveDraft = async (data: FormData) => {
    setIsDraftSaving(true);
    try {
      const contractData = {
        vendor_id: data.vendor_id!,
        title: data.title!,
        status: 'draft' as const,
        parties: {
          vendor: vendors.find(v => v.id === data.vendor_id)?.name || '',
          client: 'VMS Corp',
        },
        clauses: {
          payment_terms: data.payment_terms!,
          deliverables: data.deliverables!,
          additional_clauses: data.additional_clauses || '',
        },
        milestones: (data.milestones || []).map((milestone, index) => ({
          id: index + 1,
          name: milestone.name || '',
          description: milestone.description || '',
          due_date: milestone.due_date ? new Date(milestone.due_date).toISOString().split('T')[0] : '',
          completed: false,
        })),
        amendments: [],
        documents: [],
        start_date: data.start_date ? new Date(data.start_date).toISOString().split('T')[0] : '',
        end_date: data.end_date ? new Date(data.end_date).toISOString().split('T')[0] : '',
        value: data.value,
        currency: data.currency || 'USD',
      };

      await dispatch(createContractAsync(contractData)).unwrap();
      toast.success('Contract draft saved successfully');
      navigate('/contracts/list');
    } catch (error) {
      console.error('Failed to save draft:', error);
      toast.error('Failed to save contract draft');
    } finally {
      setIsDraftSaving(false);
    }
  };

  const onSubmit = async (data: FormData) => {
    try {
      const contractData = {
        vendor_id: data.vendor_id!,
        title: data.title!,
        status: 'active' as const,
        parties: {
          vendor: vendors.find(v => v.id === data.vendor_id)?.name || '',
          client: 'VMS Corp',
        },
        clauses: {
          payment_terms: data.payment_terms!,
          deliverables: data.deliverables!,
          additional_clauses: data.additional_clauses || '',
        },
        milestones: (data.milestones || []).map((milestone, index) => ({
          id: index + 1,
          name: milestone.name || '',
          description: milestone.description || '',
          due_date: milestone.due_date ? new Date(milestone.due_date).toISOString().split('T')[0] : '',
          completed: false,
        })),
        amendments: [],
        documents: [],
        start_date: data.start_date ? new Date(data.start_date).toISOString().split('T')[0] : '',
        end_date: data.end_date ? new Date(data.end_date).toISOString().split('T')[0] : '',
        value: data.value,
        currency: data.currency || 'USD',
      };

      await dispatch(createContractAsync(contractData)).unwrap();
      toast.success('Contract created successfully');
      navigate('/contracts/list');
    } catch (error) {
      console.error('Failed to create contract:', error);
      toast.error('Failed to create contract');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-subtle p-6">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <Button
            variant="ghost"
            onClick={() => navigate('/contracts/list')}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Contracts
          </Button>

          <div className="flex items-center gap-3 mb-2">
            <FileText className="w-8 h-8 text-primary" />
            <h1 className="text-3xl font-bold text-foreground">Create New Contract</h1>
          </div>
          <p className="text-muted-foreground">
            Create a new contract using a template or from scratch
          </p>
        </motion.div>

        {/* Template Selection */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-6"
        >
          <Card className="card-neumorphic">
            <CardHeader>
              <CardTitle>Choose a Template</CardTitle>
              <CardDescription>
                Select a contract template to get started quickly
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                {templates.map((template) => (
                  <button
                    key={template.id}
                    type="button"
                    onClick={() => handleTemplateSelect(template.id)}
                    className={`p-4 rounded-lg border text-left transition-all ${
                      selectedTemplate === template.id
                        ? 'bg-primary/10 border-primary'
                        : 'bg-background border-border hover:border-primary/50'
                    }`}
                  >
                    <h4 className="font-medium mb-1">{template.name}</h4>
                    <p className="text-sm text-muted-foreground">{template.description}</p>
                    <p className="text-xs text-muted-foreground mt-1">Category: {template.category}</p>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Contract Details
                </CardTitle>
                <CardDescription>
                  Enter the basic contract information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Contract Title *</Label>
                    <Controller
                      name="title"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="Enter contract title"
                          className={errors.title ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.title && (
                      <p className="text-sm text-destructive mt-1">{errors.title.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="vendor_id">Vendor *</Label>
                    <Controller
                      name="vendor_id"
                      control={control}
                      render={({ field }) => (
                        <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                          <SelectTrigger className={errors.vendor_id ? 'border-destructive' : ''}>
                            <SelectValue placeholder="Select vendor" />
                          </SelectTrigger>
                          <SelectContent>
                            {vendors.map((vendor) => (
                              <SelectItem key={vendor.id} value={vendor.id.toString()}>
                                <div className="flex items-center gap-2">
                                  <Building2 className="w-4 h-4" />
                                  <span>{vendor.name}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.vendor_id && (
                      <p className="text-sm text-destructive mt-1">{errors.vendor_id.message}</p>
                    )}
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start_date">Start Date *</Label>
                    <Controller
                      name="start_date"
                      control={control}
                      render={({ field }) => (
                        <Input
                          type="date"
                          value={field.value ? new Date(field.value).toISOString().split('T')[0] : ''}
                          onChange={(e) => field.onChange(e.target.value ? new Date(e.target.value) : undefined)}
                          className={errors.start_date ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.start_date && (
                      <p className="text-sm text-destructive mt-1">{errors.start_date.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="end_date">End Date *</Label>
                    <Controller
                      name="end_date"
                      control={control}
                      render={({ field }) => (
                        <Input
                          type="date"
                          value={field.value ? new Date(field.value).toISOString().split('T')[0] : ''}
                          onChange={(e) => field.onChange(e.target.value ? new Date(e.target.value) : undefined)}
                          className={errors.end_date ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.end_date && (
                      <p className="text-sm text-destructive mt-1">{errors.end_date.message}</p>
                    )}
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="value">Contract Value</Label>
                    <Controller
                      name="value"
                      control={control}
                      render={({ field }) => (
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="Enter contract value"
                          value={field.value || ''}
                          onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          className={errors.value ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.value && (
                      <p className="text-sm text-destructive mt-1">{errors.value.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="currency">Currency</Label>
                    <Controller
                      name="currency"
                      control={control}
                      render={({ field }) => (
                        <Select onValueChange={field.onChange} value={field.value || 'USD'}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD - US Dollar</SelectItem>
                            <SelectItem value="EUR">EUR - Euro</SelectItem>
                            <SelectItem value="GBP">GBP - British Pound</SelectItem>
                            <SelectItem value="CAD">CAD - Canadian Dollar</SelectItem>
                            <SelectItem value="AUD">AUD - Australian Dollar</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Terms & Deliverables */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Terms & Deliverables
                </CardTitle>
                <CardDescription>
                  Define payment terms and project deliverables
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="payment_terms">Payment Terms *</Label>
                  <Controller
                    name="payment_terms"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="e.g., 30 days net, Monthly in advance"
                        className={errors.payment_terms ? 'border-destructive' : ''}
                      />
                    )}
                  />
                  {errors.payment_terms && (
                    <p className="text-sm text-destructive mt-1">{errors.payment_terms.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="deliverables">Deliverables *</Label>
                  <Controller
                    name="deliverables"
                    control={control}
                    render={({ field }) => (
                      <Textarea
                        {...field}
                        placeholder="Describe the expected deliverables and outcomes"
                        rows={4}
                        className={errors.deliverables ? 'border-destructive' : ''}
                      />
                    )}
                  />
                  {errors.deliverables && (
                    <p className="text-sm text-destructive mt-1">{errors.deliverables.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="additional_clauses">Additional Clauses</Label>
                  <Controller
                    name="additional_clauses"
                    control={control}
                    render={({ field }) => (
                      <Textarea
                        {...field}
                        placeholder="Any additional terms, conditions, or special clauses"
                        rows={3}
                      />
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Milestones */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Milestones
                </CardTitle>
                <CardDescription>
                  Add project milestones and deadlines (optional)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {milestoneFields.map((field, index) => (
                  <div key={field.id} className="p-4 border rounded-lg space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Milestone {index + 1}</h4>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeMilestone(index)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor={`milestones.${index}.name`}>Milestone Name *</Label>
                        <Controller
                          name={`milestones.${index}.name`}
                          control={control}
                          render={({ field }) => (
                            <Input
                              {...field}
                              placeholder="e.g., Phase 1 Completion"
                              className={errors.milestones?.[index]?.name ? 'border-destructive' : ''}
                            />
                          )}
                        />
                        {errors.milestones?.[index]?.name && (
                          <p className="text-sm text-destructive mt-1">
                            {errors.milestones[index]?.name?.message}
                          </p>
                        )}
                      </div>

                      <div>
                        <Label htmlFor={`milestones.${index}.due_date`}>Due Date *</Label>
                        <Controller
                          name={`milestones.${index}.due_date`}
                          control={control}
                          render={({ field }) => (
                            <Input
                              type="date"
                              value={field.value ? new Date(field.value).toISOString().split('T')[0] : ''}
                              onChange={(e) => field.onChange(e.target.value ? new Date(e.target.value) : undefined)}
                              className={errors.milestones?.[index]?.due_date ? 'border-destructive' : ''}
                            />
                          )}
                        />
                        {errors.milestones?.[index]?.due_date && (
                          <p className="text-sm text-destructive mt-1">
                            {errors.milestones[index]?.due_date?.message}
                          </p>
                        )}
                      </div>
                    </div>

                    <div>
                      <Label htmlFor={`milestones.${index}.description`}>Description</Label>
                      <Controller
                        name={`milestones.${index}.description`}
                        control={control}
                        render={({ field }) => (
                          <Textarea
                            {...field}
                            placeholder="Optional milestone description"
                            rows={2}
                          />
                        )}
                      />
                    </div>
                  </div>
                ))}

                <Button
                  type="button"
                  variant="outline"
                  onClick={addMilestone}
                  className="w-full"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Milestone
                </Button>
              </CardContent>
            </Card>
          </motion.div>

          {/* Submit Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="flex justify-end gap-4"
          >
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/contracts/list')}
            >
              Cancel
            </Button>

            <Button
              type="button"
              variant="outline"
              onClick={handleSubmit(saveDraft)}
              disabled={isSubmitting || isDraftSaving}
              className="min-w-32"
            >
              {isDraftSaving ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
                />
              ) : (
                <>
                  <FileText className="w-4 h-4 mr-2" />
                  Save Draft
                </>
              )}
            </Button>

            <Button
              type="submit"
              disabled={isSubmitting || isDraftSaving}
              className="min-w-32 gradient-primary"
            >
              {isSubmitting ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
                />
              ) : (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Create Contract
                </>
              )}
            </Button>
          </motion.div>
        </form>
      </div>
    </div>
  );
}