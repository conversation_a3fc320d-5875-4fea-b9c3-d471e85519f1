import React, { useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { ArrowLeft, FileText, Building2, Calendar, Users, Save, AlertTriangle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { fetchVendorsAsync } from '@/store/slices/vendorsSlice';
import { createContractAsync, fetchContractTemplatesAsync } from '@/store/slices/contractsSlice';
import { RootState, AppDispatch } from '@/store';
import { useAuth } from '@/hooks/useAuth';
import { toast } from '@/components/ui/sonner';

const schema = yup.object({
  title: yup.string().required('Contract title is required'),
  vendor_id: yup.number().required('Vendor selection is required'),
  start_date: yup.date().required('Start date is required'),
  end_date: yup.date()
    .required('End date is required')
    .min(yup.ref('start_date'), 'End date must be after start date'),
  payment_terms: yup.string().required('Payment terms are required'),
  deliverables: yup.string().required('Deliverables description is required'),
  value: yup.number().positive('Contract value must be positive').optional(),
  currency: yup.string().optional(),
  milestone_name: yup.string().optional(),
  milestone_date: yup.date().optional(),
  additional_clauses: yup.string().optional(),
});

type FormData = yup.InferType<typeof schema>;



export default function ContractCreate() {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const [searchParams] = useSearchParams();
  const { canEdit } = useAuth();
  
  const { vendors } = useSelector((state: RootState) => state.vendors);
  const { templates, isCreating } = useSelector((state: RootState) => state.contracts);
  const [selectedTemplate, setSelectedTemplate] = React.useState<string>('');
  
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<FormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      title: '',
      vendor_id: undefined,
      start_date: undefined,
      end_date: undefined,
      payment_terms: '',
      deliverables: '',
      value: undefined,
      currency: 'USD',
      milestone_name: '',
      milestone_date: undefined,
      additional_clauses: '',
    },
  });

  useEffect(() => {
    dispatch(fetchVendorsAsync({}));
    dispatch(fetchContractTemplatesAsync());
  }, [dispatch]);

  // Pre-fill vendor if provided in URL
  useEffect(() => {
    const vendorId = searchParams.get('vendorId');
    if (vendorId) {
      setValue('vendor_id', parseInt(vendorId), { shouldDirty: true });
    }
  }, [searchParams, setValue]);

  if (!canEdit()) {
    return (
      <div className="min-h-screen bg-gradient-subtle flex items-center justify-center">
        <Card className="card-neumorphic max-w-md">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-destructive mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
            <p className="text-muted-foreground mb-4">
              You don't have permission to create contracts.
            </p>
            <Button onClick={() => navigate('/contracts/list')}>
              View Contracts
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    const template = contractTemplates.find(t => t.id === templateId);
    if (template) {
      setValue('title', template.name);
      setValue('payment_terms', template.defaultPaymentTerms);
      setValue('deliverables', template.defaultDeliverables);
    }
  };

  const onSubmit = async (data: FormData) => {
    try {
      // Create contract object
      const contractData = {
        ...data,
        status: 'draft' as const,
        parties: {
          vendor: vendors.find(v => v.id === data.vendor_id)?.name || '',
          client: 'VMS Corp',
        },
        clauses: {
          payment_terms: data.payment_terms,
          deliverables: data.deliverables,
          additional_clauses: data.additional_clauses || '',
        },
        milestones: data.milestone_name && data.milestone_date ? [{
          name: data.milestone_name,
          due_date: data.milestone_date,
          completed: false,
        }] : [],
        amendments: [],
      };
      
      console.log('Creating contract:', contractData);
      navigate('/contracts/list');
    } catch (error) {
      console.error('Failed to create contract:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-subtle p-6">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <Button
            variant="ghost"
            onClick={() => navigate('/contracts/list')}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Contracts
          </Button>
          
          <div className="flex items-center gap-3 mb-2">
            <FileText className="w-8 h-8 text-primary" />
            <h1 className="text-3xl font-bold text-foreground">Create New Contract</h1>
          </div>
          <p className="text-muted-foreground">
            Create a new contract using a template or from scratch
          </p>
        </motion.div>

        {/* Template Selection */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-6"
        >
          <Card className="card-neumorphic">
            <CardHeader>
              <CardTitle>Choose a Template</CardTitle>
              <CardDescription>
                Select a contract template to get started quickly
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-2 gap-4">
                {contractTemplates.map((template) => (
                  <button
                    key={template.id}
                    type="button"
                    onClick={() => handleTemplateSelect(template.id)}
                    className={`p-4 rounded-lg border text-left transition-all ${
                      selectedTemplate === template.id
                        ? 'bg-primary/10 border-primary'
                        : 'bg-background border-border hover:border-primary/50'
                    }`}
                  >
                    <h4 className="font-medium mb-1">{template.name}</h4>
                    <p className="text-sm text-muted-foreground">{template.description}</p>
                  </button>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Contract Details
                </CardTitle>
                <CardDescription>
                  Enter the basic contract information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Contract Title *</Label>
                    <Controller
                      name="title"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="Enter contract title"
                          className={errors.title ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.title && (
                      <p className="text-sm text-destructive mt-1">{errors.title.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="vendor_id">Vendor *</Label>
                    <Controller
                      name="vendor_id"
                      control={control}
                      render={({ field }) => (
                        <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                          <SelectTrigger className={errors.vendor_id ? 'border-destructive' : ''}>
                            <SelectValue placeholder="Select vendor" />
                          </SelectTrigger>
                          <SelectContent>
                            {vendors.map((vendor) => (
                              <SelectItem key={vendor.id} value={vendor.id.toString()}>
                                <div className="flex items-center gap-2">
                                  <Building2 className="w-4 h-4" />
                                  <span>{vendor.name}</span>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    {errors.vendor_id && (
                      <p className="text-sm text-destructive mt-1">{errors.vendor_id.message}</p>
                    )}
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start_date">Start Date *</Label>
                    <Controller
                      name="start_date"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="date"
                          className={errors.start_date ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.start_date && (
                      <p className="text-sm text-destructive mt-1">{errors.start_date.message}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="end_date">End Date *</Label>
                    <Controller
                      name="end_date"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="date"
                          className={errors.end_date ? 'border-destructive' : ''}
                        />
                      )}
                    />
                    {errors.end_date && (
                      <p className="text-sm text-destructive mt-1">{errors.end_date.message}</p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Terms & Deliverables */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Terms & Deliverables
                </CardTitle>
                <CardDescription>
                  Define payment terms and project deliverables
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="payment_terms">Payment Terms *</Label>
                  <Controller
                    name="payment_terms"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="e.g., 30 days net, Monthly in advance"
                        className={errors.payment_terms ? 'border-destructive' : ''}
                      />
                    )}
                  />
                  {errors.payment_terms && (
                    <p className="text-sm text-destructive mt-1">{errors.payment_terms.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="deliverables">Deliverables *</Label>
                  <Controller
                    name="deliverables"
                    control={control}
                    render={({ field }) => (
                      <Textarea
                        {...field}
                        placeholder="Describe the expected deliverables and outcomes"
                        rows={4}
                        className={errors.deliverables ? 'border-destructive' : ''}
                      />
                    )}
                  />
                  {errors.deliverables && (
                    <p className="text-sm text-destructive mt-1">{errors.deliverables.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="additional_clauses">Additional Clauses</Label>
                  <Controller
                    name="additional_clauses"
                    control={control}
                    render={({ field }) => (
                      <Textarea
                        {...field}
                        placeholder="Any additional terms, conditions, or special clauses"
                        rows={3}
                      />
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Milestones */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="w-5 h-5" />
                  Milestones
                </CardTitle>
                <CardDescription>
                  Add project milestones and deadlines (optional)
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="milestone_name">Milestone Name</Label>
                    <Controller
                      name="milestone_name"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          placeholder="e.g., Phase 1 Completion"
                        />
                      )}
                    />
                  </div>

                  <div>
                    <Label htmlFor="milestone_date">Due Date</Label>
                    <Controller
                      name="milestone_date"
                      control={control}
                      render={({ field }) => (
                        <Input
                          {...field}
                          type="date"
                        />
                      )}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Submit Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="flex justify-end gap-4"
          >
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/contracts/list')}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-32"
            >
              {isSubmitting ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
                />
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  Create Contract
                </>
              )}
            </Button>
          </motion.div>
        </form>
      </div>
    </div>
  );
}