import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Building2, 
  Mail, 
  Phone, 
  MapPin, 
  Award, 
  Calendar,
  Globe,
  Edit,
  BarChart3,
  FileText,
  MessageSquare,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { fetchVendorByIdAsync, clearCurrentVendor } from '@/store/slices/vendorsSlice';
import { RootState, AppDispatch } from '@/store';
import { useAuth } from '@/hooks/useAuth';

export default function VendorProfile() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { canEdit } = useAuth();
  
  const { currentVendor, isLoading, error } = useSelector((state: RootState) => state.vendors);
  
  useEffect(() => {
    if (id) {
      dispatch(fetchVendorByIdAsync(parseInt(id)));
    }
    
    return () => {
      dispatch(clearCurrentVendor());
    };
  }, [dispatch, id]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-subtle flex items-center justify-center">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full"
        />
      </div>
    );
  }

  if (error || !currentVendor) {
    return (
      <div className="min-h-screen bg-gradient-subtle flex items-center justify-center">
        <Card className="card-neumorphic max-w-md">
          <CardContent className="p-6 text-center">
            <AlertTriangle className="w-12 h-12 text-destructive mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Vendor Not Found</h2>
            <p className="text-muted-foreground mb-4">
              {error || 'The requested vendor could not be found.'}
            </p>
            <Button onClick={() => navigate('/vendors/list')}>
              Back to Vendors
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'inactive':
        return <XCircle className="w-4 h-4 text-yellow-500" />;
      case 'blacklisted':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'blacklisted':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-subtle p-6">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <Button
            variant="ghost"
            onClick={() => navigate('/vendors/list')}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Vendors
          </Button>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-primary/10 rounded-xl">
                <Building2 className="w-8 h-8 text-primary" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-foreground">{currentVendor.name}</h1>
                <div className="flex items-center gap-2 mt-1">
                  <Badge className={getStatusColor(currentVendor.status)}>
                    {getStatusIcon(currentVendor.status)}
                    <span className="ml-1 capitalize">{currentVendor.status}</span>
                  </Badge>
                  <Badge variant="outline">{currentVendor.category}</Badge>
                </div>
              </div>
            </div>
            
            {canEdit() && (
              <Button onClick={() => navigate(`/vendors/${id}/edit`)}>
                <Edit className="w-4 h-4 mr-2" />
                Edit Vendor
              </Button>
            )}
          </div>
        </motion.div>

        <Tabs defaultValue="details" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="contracts">Contracts</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="comments">Comments</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-6">
            <div className="grid lg:grid-cols-3 gap-6">
              {/* Contact Information */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Card className="card-neumorphic">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Mail className="w-5 h-5" />
                      Contact Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Mail className="w-4 h-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">Email</p>
                        <p className="font-medium">{currentVendor.contact_email}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <Phone className="w-4 h-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm text-muted-foreground">Phone</p>
                        <p className="font-medium">{currentVendor.contact_phone}</p>
                      </div>
                    </div>
                    
                    {currentVendor.custom_fields?.website && (
                      <div className="flex items-center gap-3">
                        <Globe className="w-4 h-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm text-muted-foreground">Website</p>
                          <a
                            href={currentVendor.custom_fields.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="font-medium text-primary hover:underline"
                          >
                            {currentVendor.custom_fields.website}
                          </a>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>

              {/* Address */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <Card className="card-neumorphic">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <MapPin className="w-5 h-5" />
                      Address
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-1">
                      <p className="font-medium">{currentVendor.address.street}</p>
                      <p className="text-muted-foreground">
                        {currentVendor.address.city}, {currentVendor.address.state} {currentVendor.address.zip}
                      </p>
                      <p className="text-muted-foreground">{currentVendor.address.country}</p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Performance Score */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <Card className="card-neumorphic">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BarChart3 className="w-5 h-5" />
                      Performance Score
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-2xl font-bold">{currentVendor.performance_score.toFixed(1)}</span>
                        <span className="text-sm text-muted-foreground">/ 100</span>
                      </div>
                      <Progress value={currentVendor.performance_score} className="h-2" />
                      <p className="text-sm text-muted-foreground">
                        {currentVendor.performance_score >= 90 ? 'Excellent' :
                         currentVendor.performance_score >= 80 ? 'Good' :
                         currentVendor.performance_score >= 70 ? 'Average' : 'Needs Improvement'}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </div>

            {/* Certifications */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card className="card-neumorphic">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Award className="w-5 h-5" />
                    Certifications
                  </CardTitle>
                  <CardDescription>
                    Industry standards and certifications held by this vendor
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {currentVendor.certifications.length > 0 ? (
                    <div className="flex flex-wrap gap-2">
                      {currentVendor.certifications.map((cert, index) => (
                        <Badge key={index} variant="secondary" className="px-3 py-1">
                          <Award className="w-3 h-3 mr-1" />
                          {cert}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground">No certifications on file</p>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {/* Additional Information */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <Card className="card-neumorphic">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="w-5 h-5" />
                    Timeline
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Created</span>
                    <span className="font-medium">
                      {new Date(currentVendor.created_at).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Last Updated</span>
                    <span className="font-medium">
                      {new Date(currentVendor.updated_at).toLocaleDateString()}
                    </span>
                  </div>
                  {currentVendor.deactivated_at && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Deactivated</span>
                      <span className="font-medium text-yellow-600">
                        {new Date(currentVendor.deactivated_at).toLocaleDateString()}
                      </span>
                    </div>
                  )}
                  {currentVendor.blacklisted_reason && (
                    <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                      <p className="text-sm font-medium text-red-800 dark:text-red-200">Blacklist Reason:</p>
                      <p className="text-sm text-red-700 dark:text-red-300">{currentVendor.blacklisted_reason}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </TabsContent>

          <TabsContent value="performance">
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>
                  Detailed performance analysis and historical data
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">Performance metrics coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="contracts">
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle>Contracts</CardTitle>
                <CardDescription>
                  All contracts associated with this vendor
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No contracts found</p>
                  {canEdit() && (
                    <Button className="mt-4" onClick={() => navigate('/contracts/create')}>
                      Create Contract
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="documents">
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle>Documents</CardTitle>
                <CardDescription>
                  Upload and manage vendor-related documents
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No documents uploaded</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="comments">
            <Card className="card-neumorphic">
              <CardHeader>
                <CardTitle>Comments</CardTitle>
                <CardDescription>
                  Team discussions and notes about this vendor
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <MessageSquare className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No comments yet</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}