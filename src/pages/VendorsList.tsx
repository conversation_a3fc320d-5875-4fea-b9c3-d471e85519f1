import React, { useEffect, useState, useCallback, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useSearchParams } from "react-router-dom";
import { motion } from "framer-motion";
import {
  Search,
  Plus,
  Building2,
  RefreshCw,
  Download,
  Grid3X3,
  List,
  SlidersHorizontal,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { VendorCard } from "../components/vendors";
import { RootState, AppDispatch } from "../store";
import {
  fetchVendorsAsync,
  setFilters,
  deleteVendorAsync,
  fetchVendorCategoriesAsync,
} from "../store/slices/vendorsSlice";
import { useAuth } from "../hooks/useAuth";
import { toast } from "../components/ui/sonner";
import { useDebounce } from "../hooks/useDebounce";

type ViewMode = "grid" | "list";

export const VendorsList: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const { canEdit, canDelete } = useAuth();

  const { vendors, categories, isLoading, filters, pagination } = useSelector(
    (state: RootState) => state.vendors
  );

  // Local state
  const [searchTerm, setSearchTerm] = useState(filters.search || "");
  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [selectedCategory, setSelectedCategory] = useState(
    filters.category || ""
  );
  const [selectedStatus, setSelectedStatus] = useState(filters.status || "");

  // Debounced search term
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Callback functions (defined before useEffect hooks)
  const handleFilterChange = useCallback(
    (filterType: string, value: string) => {
      dispatch(setFilters({ [filterType]: value }));

      // Update local state
      if (filterType === "category") setSelectedCategory(value);
      if (filterType === "status") setSelectedStatus(value);
    },
    [dispatch]
  );

  // Initialize from URL params
  useEffect(() => {
    const search = searchParams.get("search") || "";
    const category = searchParams.get("category") || "";
    const status = searchParams.get("status") || "";

    setSearchTerm(search);
    setSelectedCategory(category);
    setSelectedStatus(status);

    dispatch(setFilters({ search, category, status }));
  }, [dispatch, searchParams]);

  // Fetch vendors when filters change
  useEffect(() => {
    dispatch(fetchVendorsAsync({ filters }));
  }, [dispatch, filters]);

  // Fetch categories on mount
  useEffect(() => {
    dispatch(fetchVendorCategoriesAsync());
  }, [dispatch]);

  // Update filters when debounced search term changes
  useEffect(() => {
    if (debouncedSearchTerm !== filters.search) {
      handleFilterChange("search", debouncedSearchTerm);
    }
  }, [debouncedSearchTerm, filters.search, handleFilterChange]);

  // Update URL params when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    if (filters.search) params.set("search", filters.search);
    if (filters.category) params.set("category", filters.category);
    if (filters.status) params.set("status", filters.status);

    setSearchParams(params, { replace: true });
  }, [filters, setSearchParams]);

  const handleClearFilters = useCallback(() => {
    setSearchTerm("");
    setSelectedCategory("");
    setSelectedStatus("");
    dispatch(setFilters({ search: "", category: "", status: "" }));
  }, [dispatch]);

  const handleRefresh = useCallback(() => {
    dispatch(fetchVendorsAsync({ filters }));
    toast.success("Vendor list refreshed");
  }, [dispatch, filters]);

  const handleDelete = useCallback(
    async (vendorId: number) => {
      const vendor = vendors.find((v) => v.id === vendorId);
      if (!vendor) return;

      if (
        window.confirm(`Are you sure you want to deactivate ${vendor.name}?`)
      ) {
        try {
          await dispatch(deleteVendorAsync(vendorId)).unwrap();
          toast.success(`${vendor.name} has been deactivated`);
        } catch (error) {
          toast.error("Failed to deactivate vendor", {
            description:
              error instanceof Error ? error.message : "An error occurred",
          });
        }
      }
    },
    [dispatch, vendors]
  );

  const handleExport = useCallback(() => {
    // TODO: Implement export functionality
    toast.info("Export functionality coming soon");
  }, []);

  // Memoized filtered vendors count
  const filteredVendorsCount = useMemo(() => {
    return vendors.length;
  }, [vendors.length]);

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return !!(filters.search || filters.category || filters.status);
  }, [filters]);

  if (isLoading && vendors.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="card-neumorphic p-8">
          <div className="flex items-center space-x-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="text-muted-foreground">Loading vendors...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-foreground">Vendors</h1>
          <p className="text-muted-foreground mt-2">
            {filteredVendorsCount} vendor{filteredVendorsCount !== 1 ? "s" : ""}
            {hasActiveFilters ? " found" : " total"}
          </p>
        </div>

        <div className="flex items-center space-x-3">
          {/* View Mode Toggle */}
          <div className="flex items-center bg-muted rounded-lg p-1">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("grid")}
              className="h-8 w-8 p-0"
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("list")}
              className="h-8 w-8 p-0"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>

          {/* Action Buttons */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
            className="btn-neumorphic"
          >
            <RefreshCw
              className={`w-4 h-4 mr-2 ${isLoading ? "animate-spin" : ""}`}
            />
            Refresh
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            className="btn-neumorphic"
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>

          {canEdit() && (
            <Button
              onClick={() => navigate("/vendors/create")}
              className="btn-neumorphic gradient-primary text-primary-foreground"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Vendor
            </Button>
          )}
        </div>
      </motion.div>

      {/* Search and Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="card-neumorphic p-6"
      >
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search vendors by name, email, or category..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-neumorphic pl-10 pr-10"
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchTerm("")}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-muted"
              >
                <X className="w-3 h-3" />
              </Button>
            )}
          </div>

          {/* Quick Filters */}
          <div className="flex items-center space-x-2">
            <Select
              value={selectedStatus || "all-status"}
              onValueChange={(value) =>
                handleFilterChange(
                  "status",
                  value === "all-status" ? "" : value
                )
              }
            >
              <SelectTrigger className="w-32 btn-neumorphic">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-status">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="blacklisted">Blacklisted</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={selectedCategory || "all-categories"}
              onValueChange={(value) =>
                handleFilterChange(
                  "category",
                  value === "all-categories" ? "" : value
                )
              }
            >
              <SelectTrigger className="w-40 btn-neumorphic">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-categories">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className={`btn-neumorphic ${
                showFilters ? "bg-primary/10 text-primary" : ""
              }`}
            >
              <SlidersHorizontal className="w-4 h-4 mr-2" />
              More
            </Button>

            {hasActiveFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleClearFilters}
                className="btn-neumorphic text-muted-foreground hover:text-foreground"
              >
                <X className="w-4 h-4 mr-2" />
                Clear
              </Button>
            )}
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            transition={{ duration: 0.3 }}
            className="mt-6 pt-6 border-t border-border"
          >
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Performance Score
                </label>
                <Select defaultValue="any-score">
                  <SelectTrigger className="input-neumorphic">
                    <SelectValue placeholder="Any score" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any-score">Any score</SelectItem>
                    <SelectItem value="90-100">90-100% (Excellent)</SelectItem>
                    <SelectItem value="70-89">70-89% (Good)</SelectItem>
                    <SelectItem value="50-69">50-69% (Average)</SelectItem>
                    <SelectItem value="0-49">0-49% (Poor)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Date Added
                </label>
                <Select defaultValue="any-time">
                  <SelectTrigger className="input-neumorphic">
                    <SelectValue placeholder="Any time" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any-time">Any time</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="week">This week</SelectItem>
                    <SelectItem value="month">This month</SelectItem>
                    <SelectItem value="quarter">This quarter</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Has Certifications
                </label>
                <Select defaultValue="any-cert">
                  <SelectTrigger className="input-neumorphic">
                    <SelectValue placeholder="Any" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="any-cert">Any</SelectItem>
                    <SelectItem value="yes">Yes</SelectItem>
                    <SelectItem value="no">No</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-end">
                <Button
                  onClick={handleClearFilters}
                  variant="outline"
                  className="btn-neumorphic w-full"
                >
                  Reset Filters
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </motion.div>

      {/* Loading Overlay */}
      {isLoading && vendors.length > 0 && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="fixed inset-0 bg-background/50 backdrop-blur-sm z-50 flex items-center justify-center"
        >
          <div className="card-neumorphic p-6">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <span className="text-foreground">Updating vendors...</span>
            </div>
          </div>
        </motion.div>
      )}

      {/* Vendors Grid/List */}
      <div
        className={
          viewMode === "grid"
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            : "space-y-4"
        }
      >
        {vendors.map((vendor, index) => (
          <VendorCard
            key={vendor.id}
            vendor={vendor}
            index={index}
            onDelete={canDelete() ? handleDelete : undefined}
          />
        ))}
      </div>

      {/* Empty State */}
      {vendors.length === 0 && !isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-center py-12"
        >
          <div className="card-neumorphic p-8 max-w-md mx-auto">
            <Building2 className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              {hasActiveFilters
                ? "No vendors match your filters"
                : "No vendors found"}
            </h3>
            <p className="text-muted-foreground mb-4">
              {hasActiveFilters
                ? "Try adjusting your search criteria or clearing filters"
                : "Get started by adding your first vendor to the system"}
            </p>
            <div className="flex items-center justify-center space-x-3">
              {hasActiveFilters && (
                <Button
                  variant="outline"
                  onClick={handleClearFilters}
                  className="btn-neumorphic"
                >
                  <X className="w-4 h-4 mr-2" />
                  Clear Filters
                </Button>
              )}
              {canEdit() && (
                <Button
                  onClick={() => navigate("/vendors/create")}
                  className="btn-neumorphic gradient-primary text-primary-foreground"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  {hasActiveFilters ? "Add Vendor" : "Add First Vendor"}
                </Button>
              )}
            </div>
          </div>
        </motion.div>
      )}

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-center mt-8"
        >
          <div className="card-neumorphic p-4">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page === 1}
                onClick={() => {
                  // TODO: Implement pagination
                }}
                className="btn-neumorphic"
              >
                Previous
              </Button>

              <span className="text-sm text-muted-foreground px-4">
                Page {pagination.page} of {pagination.totalPages}
              </span>

              <Button
                variant="outline"
                size="sm"
                disabled={pagination.page === pagination.totalPages}
                onClick={() => {
                  // TODO: Implement pagination
                }}
                className="btn-neumorphic"
              >
                Next
              </Button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};
