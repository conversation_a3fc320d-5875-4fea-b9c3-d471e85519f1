import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import vendorsSlice from './slices/vendorsSlice';
import contractsSlice from './slices/contractsSlice';
import invoicesSlice from './slices/invoicesSlice';
import notificationsSlice from './slices/notificationsSlice';
import uiSlice from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    vendors: vendorsSlice,
    contracts: contractsSlice,
    invoices: invoicesSlice,
    notifications: notificationsSlice,
    ui: uiSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;