import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface User {
  id: number;
  email: string;
  role: 'admin' | 'manager' | 'viewer';
  is_verified: boolean;
  full_name?: string;
  organization?: string;
  preferences: {
    lang: string;
    currency: string;
    theme: string;
  };
  consent_given: boolean;
  created_at: string;
  updated_at: string;
}

export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  fullName: string;
  organization: string;
  acceptTerms: boolean;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  token: string;
  password: string;
  confirmPassword: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  verificationStatus: 'pending' | 'verified' | 'expired' | null;
  resetTokenValid: boolean;
}

const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('vms_token'),
  isLoading: false,
  error: null,
  isAuthenticated: false,
  verificationStatus: null,
  resetTokenValid: false,
};

// Mock email service for development
const mockEmailService = {
  sendVerificationEmail: async (email: string, token: string) => {
    console.log(`📧 Verification email sent to ${email} with token: ${token}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  },
  sendPasswordResetEmail: async (email: string, token: string) => {
    console.log(`📧 Password reset email sent to ${email} with token: ${token}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  }
};

// Mock token storage for verification and reset tokens
const mockTokenStorage: Record<string, { email: string; expires: number; type: 'verification' | 'reset' }> = {};

// Mock user storage for registration
const mockUserStorage: Record<string, User> = {};

// Mock authentication - simulate API calls
export const registerAsync = createAsyncThunk(
  'auth/register',
  async (data: RegisterData) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if user already exists
    if (mockUserStorage[data.email]) {
      throw new Error('Email already registered');
    }
    
    // Create new user
    const newUser: User = {
      id: Date.now(),
      email: data.email,
      role: 'viewer',
      is_verified: false,
      full_name: data.fullName,
      organization: data.organization,
      preferences: { lang: 'en', currency: 'USD', theme: 'light' },
      consent_given: data.acceptTerms,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    // Store user
    mockUserStorage[data.email] = newUser;
    
    // Generate verification token
    const verificationToken = `verify_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    mockTokenStorage[verificationToken] = {
      email: data.email,
      expires: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
      type: 'verification'
    };
    
    // Send verification email
    await mockEmailService.sendVerificationEmail(data.email, verificationToken);
    
    return { user: newUser, verificationToken };
  }
);

export const forgotPasswordAsync = createAsyncThunk(
  'auth/forgotPassword',
  async (data: ForgotPasswordData) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if user exists (in mock storage or default users)
    const userExists = mockUserStorage[data.email] || 
      ['<EMAIL>', '<EMAIL>', '<EMAIL>'].includes(data.email);
    
    if (!userExists) {
      throw new Error('Email not found');
    }
    
    // Generate reset token
    const resetToken = `reset_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    mockTokenStorage[resetToken] = {
      email: data.email,
      expires: Date.now() + (60 * 60 * 1000), // 1 hour
      type: 'reset'
    };
    
    // Send reset email
    await mockEmailService.sendPasswordResetEmail(data.email, resetToken);
    
    return { email: data.email, resetToken };
  }
);

export const resetPasswordAsync = createAsyncThunk(
  'auth/resetPassword',
  async (data: ResetPasswordData) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Validate token
    const tokenData = mockTokenStorage[data.token];
    if (!tokenData || tokenData.type !== 'reset' || tokenData.expires < Date.now()) {
      throw new Error('Invalid or expired reset token');
    }
    
    // Update password (in real app, hash the password)
    console.log(`🔐 Password updated for ${tokenData.email}`);
    
    // Remove used token
    delete mockTokenStorage[data.token];
    
    return { email: tokenData.email };
  }
);

export const verifyEmailAsync = createAsyncThunk(
  'auth/verifyEmail',
  async (token: string) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Validate token
    const tokenData = mockTokenStorage[token];
    if (!tokenData || tokenData.type !== 'verification' || tokenData.expires < Date.now()) {
      throw new Error('Invalid or expired verification token');
    }
    
    // Update user verification status
    if (mockUserStorage[tokenData.email]) {
      mockUserStorage[tokenData.email].is_verified = true;
      mockUserStorage[tokenData.email].updated_at = new Date().toISOString();
    }
    
    // Remove used token
    delete mockTokenStorage[token];
    
    return { email: tokenData.email };
  }
);

export const resendVerificationAsync = createAsyncThunk(
  'auth/resendVerification',
  async (email: string) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Check if user exists
    const userExists = mockUserStorage[email];
    if (!userExists) {
      throw new Error('User not found');
    }
    
    // Generate new verification token
    const verificationToken = `verify_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    mockTokenStorage[verificationToken] = {
      email: email,
      expires: Date.now() + (24 * 60 * 60 * 1000), // 24 hours
      type: 'verification'
    };
    
    // Send verification email
    await mockEmailService.sendVerificationEmail(email, verificationToken);
    
    return { email, verificationToken };
  }
);

export const loginAsync = createAsyncThunk(
  'auth/login',
  async ({ email, password }: { email: string; password: string }) => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock user data based on email
    const mockUsers: Record<string, User> = {
      '<EMAIL>': {
        id: 1,
        email: '<EMAIL>',
        role: 'admin',
        is_verified: true,
        full_name: 'System Administrator',
        organization: 'VendorMS Corp',
        preferences: { lang: 'en', currency: 'USD', theme: 'light' },
        consent_given: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-26T00:00:00Z',
      },
      '<EMAIL>': {
        id: 2,
        email: '<EMAIL>',
        role: 'manager',
        is_verified: true,
        full_name: 'Procurement Manager',
        organization: 'VendorMS Corp',
        preferences: { lang: 'en', currency: 'USD', theme: 'light' },
        consent_given: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-26T00:00:00Z',
      },
      '<EMAIL>': {
        id: 3,
        email: '<EMAIL>',
        role: 'viewer',
        is_verified: true,
        full_name: 'System Viewer',
        organization: 'VendorMS Corp',
        preferences: { lang: 'en', currency: 'USD', theme: 'light' },
        consent_given: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-26T00:00:00Z',
      },
    };

    if (password === 'password' && mockUsers[email]) {
      const user = mockUsers[email];
      
      // Check if user exists in registered users (from registration flow)
      const registeredUser = mockUserStorage[email];
      if (registeredUser && !registeredUser.is_verified) {
        throw new Error('Please verify your email address before signing in');
      }
      
      const token = `mock_jwt_token_${Date.now()}`;
      localStorage.setItem('vms_token', token);
      return { user, token };
    } else {
      throw new Error('Invalid credentials');
    }
  }
);

export const logoutAsync = createAsyncThunk('auth/logout', async () => {
  localStorage.removeItem('vms_token');
  return null;
});

export const verifyTokenAsync = createAsyncThunk('auth/verifyToken', async () => {
  const token = localStorage.getItem('vms_token');
  if (!token) throw new Error('No token found');
  
  // Simulate token verification
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Mock user based on stored token (in real app, decode from JWT)
  const mockUser: User = {
    id: 1,
    email: '<EMAIL>',
    role: 'admin',
    is_verified: true,
    preferences: { lang: 'en', currency: 'USD', theme: 'light' },
    consent_given: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-26T00:00:00Z',
  };
  
  return { user: mockUser, token };
});

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateUserPreferences: (state, action: PayloadAction<Partial<User['preferences']>>) => {
      if (state.user) {
        state.user.preferences = { ...state.user.preferences, ...action.payload };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Login
      .addCase(loginAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
      })
      .addCase(loginAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Login failed';
        state.isAuthenticated = false;
      })
      // Logout
      .addCase(logoutAsync.fulfilled, (state) => {
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        state.error = null;
      })
      // Verify Token
      .addCase(verifyTokenAsync.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(verifyTokenAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isAuthenticated = true;
      })
      .addCase(verifyTokenAsync.rejected, (state) => {
        state.isLoading = false;
        state.user = null;
        state.token = null;
        state.isAuthenticated = false;
        localStorage.removeItem('vms_token');
      })
      // Register
      .addCase(registerAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.verificationStatus = 'pending';
        state.error = null;
      })
      .addCase(registerAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Registration failed';
      })
      // Forgot Password
      .addCase(forgotPasswordAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(forgotPasswordAsync.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
      })
      .addCase(forgotPasswordAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to send reset email';
      })
      // Reset Password
      .addCase(resetPasswordAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
        state.resetTokenValid = true;
      })
      .addCase(resetPasswordAsync.fulfilled, (state) => {
        state.isLoading = false;
        state.error = null;
        state.resetTokenValid = false;
      })
      .addCase(resetPasswordAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Password reset failed';
        state.resetTokenValid = false;
      })
      // Verify Email
      .addCase(verifyEmailAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyEmailAsync.fulfilled, (state) => {
        state.isLoading = false;
        state.verificationStatus = 'verified';
        state.error = null;
      })
      .addCase(verifyEmailAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.verificationStatus = 'expired';
        state.error = action.error.message || 'Email verification failed';
      })
      // Resend Verification
      .addCase(resendVerificationAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(resendVerificationAsync.fulfilled, (state) => {
        state.isLoading = false;
        state.verificationStatus = 'pending';
        state.error = null;
      })
      .addCase(resendVerificationAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to resend verification email';
      });
  },
});

export const { clearError, updateUserPreferences } = authSlice.actions;
export default authSlice.reducer;