import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export interface Milestone {
  id: number;
  name: string;
  description?: string;
  due_date: string;
  completed: boolean;
  completed_date?: string;
}

export interface Amendment {
  id: number;
  description: string;
  changes: Record<string, any>;
  created_at: string;
  created_by: number;
}

export interface ContractDocument {
  id: number;
  name: string;
  type: string;
  url: string;
  uploaded_at: string;
  uploaded_by: number;
}

export interface Contract {
  id: number;
  vendor_id: number;
  title: string;
  status: 'draft' | 'signed' | 'active' | 'expired' | 'terminated';
  parties: {
    vendor: string;
    client: string;
    additional?: string[];
  };
  clauses: {
    payment_terms: string;
    deliverables: string;
    additional_clauses?: string;
    termination_clause?: string;
  };
  milestones: Milestone[];
  start_date: string;
  end_date: string;
  value?: number;
  currency?: string;
  docusign_envelope_id?: string;
  amendments: Amendment[];
  documents: ContractDocument[];
  created_at: string;
  updated_at: string;
}

export interface ContractTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  defaultFields: {
    payment_terms: string;
    deliverables: string;
    additional_clauses: string;
  };
  requiredFields: string[];
}

export interface ContractFilters {
  search: string;
  status: string;
  vendor: string;
  dateRange: {
    start: string;
    end: string;
  };
}

interface ContractsState {
  contracts: Contract[];
  currentContract: Contract | null;
  templates: ContractTemplate[];
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  error: string | null;
  filters: ContractFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

const initialState: ContractsState = {
  contracts: [],
  currentContract: null,
  templates: [],
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  error: null,
  filters: {
    search: '',
    status: '',
    vendor: '',
    dateRange: {
      start: '',
      end: '',
    },
  },
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
};

const mockContracts: Contract[] = [
  {
    id: 1,
    vendor_id: 1,
    title: 'Software Development Agreement',
    status: 'active',
    parties: { vendor: 'TechCorp Solutions', client: 'VMS Corp' },
    clauses: { 
      payment_terms: '30 days net', 
      deliverables: 'Web application with responsive design, user authentication, and dashboard',
      additional_clauses: 'All source code to be delivered upon completion. 90-day warranty period included.'
    },
    milestones: [
      { id: 1, name: 'Phase 1: Design & Planning', due_date: '2024-03-01', completed: true, completed_date: '2024-02-28' },
      { id: 2, name: 'Phase 2: Development', due_date: '2024-04-01', completed: false },
      { id: 3, name: 'Phase 3: Testing & Deployment', due_date: '2024-05-01', completed: false },
    ],
    start_date: '2024-02-01',
    end_date: '2024-05-01',
    value: 50000,
    currency: 'USD',
    amendments: [],
    documents: [],
    created_at: '2024-01-15T10:30:00Z',
    updated_at: '2024-01-20T14:22:00Z',
  },
  {
    id: 2,
    vendor_id: 2,
    title: 'Marketing Services Agreement',
    status: 'draft',
    parties: { vendor: 'Creative Marketing Co', client: 'VMS Corp' },
    clauses: { 
      payment_terms: '15 days net', 
      deliverables: 'Marketing campaign design, social media management, and performance analytics',
      additional_clauses: 'Monthly performance reports required. Campaign materials subject to approval.'
    },
    milestones: [
      { id: 4, name: 'Campaign Strategy', due_date: '2024-03-15', completed: false },
      { id: 5, name: 'Creative Development', due_date: '2024-04-01', completed: false },
    ],
    start_date: '2024-03-01',
    end_date: '2024-08-31',
    value: 25000,
    currency: 'USD',
    amendments: [],
    documents: [],
    created_at: '2024-02-01T09:15:00Z',
    updated_at: '2024-02-01T09:15:00Z',
  },
  {
    id: 3,
    vendor_id: 3,
    title: 'IT Support Services',
    status: 'terminated',
    parties: { vendor: 'TechSupport Pro', client: 'VMS Corp' },
    clauses: { 
      payment_terms: 'Monthly in advance', 
      deliverables: 'IT support services, system maintenance, and help desk support',
      additional_clauses: 'Service level agreement: 99.5% uptime guarantee. 24/7 support coverage.',
      termination_clause: 'Terminated due to SLA violations'
    },
    milestones: [],
    start_date: '2023-06-01',
    end_date: '2024-01-31',
    value: 120000,
    currency: 'USD',
    amendments: [
      {
        id: 1,
        description: 'Extended support hours',
        changes: { support_hours: '24/7' },
        created_at: '2023-08-15T14:30:00Z',
        created_by: 1
      }
    ],
    documents: [],
    created_at: '2023-05-15T11:20:00Z',
    updated_at: '2024-01-31T16:45:00Z',
  },
];

const mockTemplates: ContractTemplate[] = [
  {
    id: 'software-dev',
    name: 'Software Development Agreement',
    description: 'Standard template for software development projects',
    category: 'Technology',
    defaultFields: {
      payment_terms: '30 days net',
      deliverables: 'Software application with source code, documentation, and testing',
      additional_clauses: 'All intellectual property rights transfer to client upon final payment. 90-day warranty period included.'
    },
    requiredFields: ['title', 'vendor_id', 'start_date', 'end_date', 'payment_terms', 'deliverables']
  },
  {
    id: 'consulting',
    name: 'Consulting Services Agreement',
    description: 'Professional consulting and advisory services',
    category: 'Services',
    defaultFields: {
      payment_terms: '15 days net',
      deliverables: 'Consulting services, reports, and recommendations',
      additional_clauses: 'Confidentiality agreement applies to all shared information. Reports delivered in digital format.'
    },
    requiredFields: ['title', 'vendor_id', 'start_date', 'end_date', 'payment_terms', 'deliverables']
  },
  {
    id: 'manufacturing',
    name: 'Manufacturing Agreement',
    description: 'Product manufacturing and supply contracts',
    category: 'Manufacturing',
    defaultFields: {
      payment_terms: '45 days net',
      deliverables: 'Manufactured products according to specifications',
      additional_clauses: 'Quality control standards must be met. Delivery schedule as per attached timeline.'
    },
    requiredFields: ['title', 'vendor_id', 'start_date', 'end_date', 'payment_terms', 'deliverables']
  },
  {
    id: 'maintenance',
    name: 'Maintenance & Support Agreement',
    description: 'Ongoing maintenance and support services',
    category: 'Services',
    defaultFields: {
      payment_terms: 'Monthly in advance',
      deliverables: 'Maintenance services, technical support, and system updates',
      additional_clauses: 'Service level agreement attached. Emergency support available 24/7.'
    },
    requiredFields: ['title', 'vendor_id', 'start_date', 'end_date', 'payment_terms', 'deliverables']
  },
];

// Async thunks
export const fetchContractsAsync = createAsyncThunk(
  'contracts/fetchContracts',
  async (params?: { filters?: Partial<ContractFilters>; page?: number; limit?: number }) => {
    await new Promise(resolve => setTimeout(resolve, 800));
    
    let filteredContracts = [...mockContracts];
    
    if (params?.filters) {
      const { search, status, vendor, dateRange } = params.filters;
      
      if (search) {
        filteredContracts = filteredContracts.filter(contract =>
          contract.title.toLowerCase().includes(search.toLowerCase()) ||
          contract.parties.vendor.toLowerCase().includes(search.toLowerCase()) ||
          contract.id.toString().includes(search)
        );
      }
      
      if (status) {
        filteredContracts = filteredContracts.filter(contract => contract.status === status);
      }
      
      if (vendor) {
        filteredContracts = filteredContracts.filter(contract => 
          contract.parties.vendor.toLowerCase().includes(vendor.toLowerCase())
        );
      }
      
      if (dateRange?.start && dateRange?.end) {
        filteredContracts = filteredContracts.filter(contract => {
          const contractStart = new Date(contract.start_date);
          const filterStart = new Date(dateRange.start);
          const filterEnd = new Date(dateRange.end);
          return contractStart >= filterStart && contractStart <= filterEnd;
        });
      }
    }
    
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const total = filteredContracts.length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    
    return {
      contracts: filteredContracts.slice(startIndex, endIndex),
      pagination: { page, limit, total, totalPages }
    };
  }
);

export const fetchContractByIdAsync = createAsyncThunk(
  'contracts/fetchContractById',
  async (id: number) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const contract = mockContracts.find(c => c.id === id);
    if (!contract) {
      throw new Error('Contract not found');
    }
    return contract;
  }
);

export const createContractAsync = createAsyncThunk(
  'contracts/createContract',
  async (contractData: Omit<Contract, 'id' | 'created_at' | 'updated_at'>) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const newContract: Contract = {
      ...contractData,
      id: Math.max(...mockContracts.map(c => c.id)) + 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    
    mockContracts.push(newContract);
    return newContract;
  }
);

export const updateContractAsync = createAsyncThunk(
  'contracts/updateContract',
  async ({ id, updates }: { id: number; updates: Partial<Contract> }) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const contractIndex = mockContracts.findIndex(c => c.id === id);
    if (contractIndex === -1) {
      throw new Error('Contract not found');
    }
    
    const updatedContract = {
      ...mockContracts[contractIndex],
      ...updates,
      updated_at: new Date().toISOString(),
    };
    
    mockContracts[contractIndex] = updatedContract;
    return updatedContract;
  }
);

export const deleteContractAsync = createAsyncThunk(
  'contracts/deleteContract',
  async (id: number) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const contractIndex = mockContracts.findIndex(c => c.id === id);
    if (contractIndex === -1) {
      throw new Error('Contract not found');
    }
    
    mockContracts.splice(contractIndex, 1);
    return id;
  }
);

export const fetchContractTemplatesAsync = createAsyncThunk(
  'contracts/fetchTemplates',
  async () => {
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockTemplates;
  }
);

const contractsSlice = createSlice({
  name: 'contracts',
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
    },
    clearCurrentContract: (state) => {
      state.currentContract = null;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch contracts
      .addCase(fetchContractsAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchContractsAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.contracts = action.payload.contracts;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchContractsAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch contracts';
      })
      
      // Fetch contract by ID
      .addCase(fetchContractByIdAsync.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchContractByIdAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentContract = action.payload;
      })
      .addCase(fetchContractByIdAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch contract';
      })
      
      // Create contract
      .addCase(createContractAsync.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createContractAsync.fulfilled, (state, action) => {
        state.isCreating = false;
        state.contracts.unshift(action.payload);
        state.currentContract = action.payload;
      })
      .addCase(createContractAsync.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.error.message || 'Failed to create contract';
      })
      
      // Update contract
      .addCase(updateContractAsync.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateContractAsync.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.contracts.findIndex(c => c.id === action.payload.id);
        if (index !== -1) {
          state.contracts[index] = action.payload;
        }
        if (state.currentContract?.id === action.payload.id) {
          state.currentContract = action.payload;
        }
      })
      .addCase(updateContractAsync.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.error.message || 'Failed to update contract';
      })
      
      // Delete contract
      .addCase(deleteContractAsync.fulfilled, (state, action) => {
        state.contracts = state.contracts.filter(c => c.id !== action.payload);
        if (state.currentContract?.id === action.payload) {
          state.currentContract = null;
        }
      })
      .addCase(deleteContractAsync.rejected, (state, action) => {
        state.error = action.error.message || 'Failed to delete contract';
      })
      
      // Fetch templates
      .addCase(fetchContractTemplatesAsync.fulfilled, (state, action) => {
        state.templates = action.payload;
      });
  },
});

export const { setFilters, clearFilters, clearCurrentContract, clearError } = contractsSlice.actions;

export default contractsSlice.reducer;