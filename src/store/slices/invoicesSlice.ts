import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export interface Invoice {
  id: number;
  contract_id: number;
  vendor_id: number;
  amount: number;
  currency: 'USD' | 'EUR' | 'GBP';
  status: 'draft' | 'sent' | 'approved' | 'paid' | 'overdue';
  items: any[];
  taxes: number;
  penalties: number;
  due_date: string;
  approved_by?: number;
  created_at: string;
  updated_at: string;
}

interface InvoicesState {
  invoices: Invoice[];
  currentInvoice: Invoice | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: InvoicesState = {
  invoices: [],
  currentInvoice: null,
  isLoading: false,
  error: null,
};

const mockInvoices: Invoice[] = [
  {
    id: 1,
    contract_id: 1,
    vendor_id: 1,
    amount: 25000,
    currency: 'USD',
    status: 'approved',
    items: [
      { description: 'Phase 1 Development', amount: 25000 },
    ],
    taxes: 2500,
    penalties: 0,
    due_date: '2024-03-01',
    approved_by: 1,
    created_at: '2024-02-01T10:30:00Z',
    updated_at: '2024-02-15T14:22:00Z',
  },
];

export const fetchInvoicesAsync = createAsyncThunk(
  'invoices/fetchInvoices',
  async () => {
    await new Promise(resolve => setTimeout(resolve, 800));
    return mockInvoices;
  }
);

const invoicesSlice = createSlice({
  name: 'invoices',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchInvoicesAsync.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchInvoicesAsync.fulfilled, (state, action) => {
        state.isLoading = false;
        state.invoices = action.payload;
      })
      .addCase(fetchInvoicesAsync.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch invoices';
      });
  },
});

export default invoicesSlice.reducer;