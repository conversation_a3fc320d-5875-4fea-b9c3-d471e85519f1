import * as yup from 'yup';

// Vendor validation schema
export const vendorValidationSchema = yup.object({
  name: yup
    .string()
    .required('Vendor name is required')
    .min(2, 'Name must be at least 2 characters')
    .max(255, 'Name must be less than 255 characters')
    .trim(),
  
  contact_email: yup
    .string()
    .email('Invalid email format')
    .required('Email is required')
    .max(255, 'Email must be less than 255 characters')
    .trim(),
  
  contact_phone: yup
    .string()
    .required('Phone number is required')
    .matches(
      /^[\+]?[1-9][\d]{0,15}$/,
      'Invalid phone number format'
    )
    .max(50, 'Phone number must be less than 50 characters'),
  
  address: yup.object({
    street: yup
      .string()
      .required('Street address is required')
      .max(255, 'Street address must be less than 255 characters')
      .trim(),
    
    city: yup
      .string()
      .required('City is required')
      .max(100, 'City must be less than 100 characters')
      .trim(),
    
    state: yup
      .string()
      .required('State is required')
      .max(100, 'State must be less than 100 characters')
      .trim(),
    
    zip: yup
      .string()
      .required('ZIP code is required')
      .max(20, 'ZIP code must be less than 20 characters')
      .trim(),
    
    country: yup
      .string()
      .required('Country is required')
      .max(100, 'Country must be less than 100 characters')
      .trim(),
  }).required('Address is required'),
  
  category: yup
    .string()
    .required('Category is required')
    .max(100, 'Category must be less than 100 characters'),
  
  certifications: yup
    .array()
    .of(yup.string().max(100, 'Certification name must be less than 100 characters'))
    .default([]),
  
  custom_fields: yup
    .object()
    .default({}),
});

// Vendor update schema (all fields optional except id)
export const vendorUpdateValidationSchema = yup.object({
  id: yup.number().required('Vendor ID is required'),
  name: yup
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(255, 'Name must be less than 255 characters')
    .trim(),
  
  contact_email: yup
    .string()
    .email('Invalid email format')
    .max(255, 'Email must be less than 255 characters')
    .trim(),
  
  contact_phone: yup
    .string()
    .matches(
      /^[\+]?[1-9][\d]{0,15}$/,
      'Invalid phone number format'
    )
    .max(50, 'Phone number must be less than 50 characters'),
  
  address: yup.object({
    street: yup
      .string()
      .max(255, 'Street address must be less than 255 characters')
      .trim(),
    
    city: yup
      .string()
      .max(100, 'City must be less than 100 characters')
      .trim(),
    
    state: yup
      .string()
      .max(100, 'State must be less than 100 characters')
      .trim(),
    
    zip: yup
      .string()
      .max(20, 'ZIP code must be less than 20 characters')
      .trim(),
    
    country: yup
      .string()
      .max(100, 'Country must be less than 100 characters')
      .trim(),
  }),
  
  category: yup
    .string()
    .max(100, 'Category must be less than 100 characters'),
  
  certifications: yup
    .array()
    .of(yup.string().max(100, 'Certification name must be less than 100 characters')),
  
  custom_fields: yup.object(),
});

// Filter validation schema
export const vendorFiltersValidationSchema = yup.object({
  search: yup.string().max(255, 'Search term must be less than 255 characters'),
  category: yup.string().max(100, 'Category must be less than 100 characters'),
  status: yup.string().oneOf(['active', 'inactive', 'blacklisted'], 'Invalid status'),
  performance_min: yup.number().min(0, 'Minimum performance must be at least 0').max(100, 'Minimum performance must be at most 100'),
  performance_max: yup.number().min(0, 'Maximum performance must be at least 0').max(100, 'Maximum performance must be at most 100'),
});

// Common validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
  return phoneRegex.test(phone);
};

export const sanitizeString = (str: string): string => {
  return str.trim().replace(/[<>]/g, '');
};

export const validateRequired = (value: any, fieldName: string): string | null => {
  if (value === null || value === undefined || value === '') {
    return `${fieldName} is required`;
  }
  return null;
};

// Form validation helper
export const validateForm = async <T>(
  schema: yup.Schema<T>,
  data: any
): Promise<{ isValid: boolean; errors: Record<string, string>; data?: T }> => {
  try {
    const validatedData = await schema.validate(data, { abortEarly: false });
    return { isValid: true, errors: {}, data: validatedData };
  } catch (error) {
    if (error instanceof yup.ValidationError) {
      const errors: Record<string, string> = {};
      error.inner.forEach((err) => {
        if (err.path) {
          errors[err.path] = err.message;
        }
      });
      return { isValid: false, errors };
    }
    return { isValid: false, errors: { general: 'Validation failed' } };
  }
};

// Export validation schemas
export default {
  vendor: vendorValidationSchema,
  vendorUpdate: vendorUpdateValidationSchema,
  vendorFilters: vendorFiltersValidationSchema,
};